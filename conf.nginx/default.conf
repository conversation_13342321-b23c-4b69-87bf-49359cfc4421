server {
    server_name baora.home.local
    listen 80;

    root  /baora;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";


    index index.php;

    # This should match the `post_max_size` and/or `upload_max_filesize` settings
    # in your php.ini.
    client_max_body_size 50M;

    # Redirect 404 errors to PrestaShop.
    error_page 404 /index.php?controller=404;

    # HSTS (Force clients to interact with your website using HTTPS only).
    # For enhanced security, register your site here: https://hstspreload.org/.
    # WARNING: Don't use this if your site is not fully on HTTPS!
    # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains" preload; always;

    # XSS Protection
    # add_header X-XSS-Protection "1; mode=block";

    # Clickjacking
    # add_header X-Frame-Options "SAMEORIGIN";

    # X-Content Type Options
    # add_header X-Content-Type-Options nosniff;

    # Secure Cookie
    # add_header Set-Cookie "Path=/; HttpOnly; Secure";


    # [EDIT] If you are using multiple languages.
    # rewrite ^/fr$ /fr/ redirect;
    # rewrite ^/fr/(.*) /$1;
    # Watch out: if you encounter issues with a quick view or shopping cart, you might want to use a different rule:
    # rewrite '^/((?!js|qq)[a-z]{2})/(.*)' /index.php?isolang=$1&$args last;
    # see: https://github.com/PrestaShop/PrestaShop/issues/14921#issuecomment-948932833

    # Images.
    rewrite ^/(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$1$2.jpg last;
    rewrite ^/(\d)(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$2/$1$2$3.jpg last;
    rewrite ^/(\d)(\d)(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$2/$3/$1$2$3$4.jpg last;
    rewrite ^/(\d)(\d)(\d)(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$2/$3/$4/$1$2$3$4$5.jpg last;
    rewrite ^/(\d)(\d)(\d)(\d)(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$2/$3/$4/$5/$1$2$3$4$5$6.jpg last;
    rewrite ^/(\d)(\d)(\d)(\d)(\d)(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$2/$3/$4/$5/$6/$1$2$3$4$5$6$7.jpg last;
    rewrite ^/(\d)(\d)(\d)(\d)(\d)(\d)(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$2/$3/$4/$5/$6/$7/$1$2$3$4$5$6$7$8.jpg last;
    rewrite ^/(\d)(\d)(\d)(\d)(\d)(\d)(\d)(\d)(-[\w-]+)?/.+\.jpg$ /img/p/$1/$2/$3/$4/$5/$6/$7/$8/$1$2$3$4$5$6$7$8$9.jpg last;
    rewrite ^/c/([\w.-]+)/.+\.jpg$ /img/c/$1.jpg last;

    # AlphaImageLoader for IE and FancyBox.
    rewrite ^images_ie/?([^/]+)\.(gif|jpe?g|png)$ js/jquery/plugins/fancybox/images/$1.$2 last;

    # Web service API.
    rewrite ^/api/?(.*)$ /webservice/dispatcher.php?url=$1 last;

    # Installation sandbox.
    rewrite ^(/install(?:-dev)?/sandbox)/.* /$1/test.php last;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # [EDIT] Replace 'admin-dev' in this block with the name of your admin directory.
    location /admin-dev/ {
        try_files $uri $uri/ /admin-dev/index.php$is_args$args;
    }


    # .htaccess, .DS_Store, .htpasswd, etc.
    location ~ /\.(?!well-known) {
        deny all;
    }

    # Source code directories.
    location ~ ^/(app|bin|cache|classes|config|controllers|docs|localization|override|src|tests|tools|translations|var|vendor)/ {
        deny all;
    }

    # vendor in modules directory.
    location ~ ^/modules/.*/vendor/ {
        deny all;
    }

    # Prevent exposing other sensitive files.
    location ~ \.(log|tpl|twig|sass|yml)$ {
        deny all;
    }

    # Prevent injection of PHP files.
    location /img {
        location ~ \.php$ { deny all; }
    }

    location /upload {
        location ~ \.php$ { deny all; }
    }

    location ~ [^/]\.php(/|$) {
        # Split $uri to $fastcgi_script_name and $fastcgi_path_info.
        fastcgi_split_path_info ^(.+?\.php)(/.*)$;

        # Ensure that the requested PHP script exists before passing it
        # to the PHP-FPM.
        try_files $fastcgi_script_name =404;

        # Environment variables for PHP.
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $request_filename;

        fastcgi_index index.php;

        fastcgi_keep_conn on;
        fastcgi_read_timeout 30s;
        fastcgi_send_timeout 30s;

        # Uncomment these in case of long loading or 502/504 errors.
        # fastcgi_buffer_size 256k;
        # fastcgi_buffers 256 16k;
        # fastcgi_busy_buffers_size 256k;

        # Connection to PHP-FPM container
        fastcgi_pass baora-php:9000;
    }

}