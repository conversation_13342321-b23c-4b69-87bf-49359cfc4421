# Doctrine Configuration
doctrine:
  dbal:
    default_connection: default

    connections:
      default:
        driver: pdo_mysql
        host: "%database_host%"
        port: "%database_port%"
        dbname: "%database_name%"
        user: "%database_user%"
        password: "%database_password%"
        charset: utf8mb4
        mapping_types:
          enum: string
        options:
          # PDO::MYSQL_ATTR_INIT_COMMAND
          !php/const PDO::MYSQL_ATTR_INIT_COMMAND: "SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))"
          # PDO::MYSQL_ATTR_MULTI_STATEMENTS
          !php/const PDO::MYSQL_ATTR_MULTI_STATEMENTS: '%env(const:runtime:_PS_ALLOW_MULTI_STATEMENTS_QUERIES_)%'

  orm:
    auto_generate_proxy_classes: "%kernel.debug%"
    naming_strategy: prestashop.database.naming_strategy
    auto_mapping: true
    dql:
      string_functions:
        regexp: DoctrineExtensions\Query\Mysql\Regexp
