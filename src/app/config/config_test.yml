parameters:
  resources_dir: "%kernel.project_dir%/tests/Resources"
  test_translations_dir: "%resources_dir%/translations"
  translations_theme_dir: "%resources_dir%/themes"
  translations_modules_dir: "%resources_dir%/modules"
  prestashop.security.voter.product.class: Tests\Integration\Utility\PageVoter
  AdapterSecurityAdminClass: Tests\Integration\Utility\AdapterSecurityAdmin

imports:
  - { resource: config.yml }
  - { resource: security_test.yml }
  - { resource: "../../tests/Resources/config/services.yml" }

framework:
  test: ~
  session:
    storage_id: session.storage.mock_file
  profiler:
    collect: false

web_profiler:
  toolbar: '%use_debug_toolbar%'
  intercept_redirects: true

swiftmailer:
  disable_delivery: true

# Doctrine Configuration
doctrine:
  dbal:
    connections:
      default:
        dbname: "test_%database_name%"

monolog:
  handlers:
    main:
      type: rotating_file
      max_files: '%env(PS_LOG_MAX_FILES)%'
      path: '%env(PS_LOG_OUTPUT)%'
      level: debug
      channels: [ "!event" ]
    console:
      type: console
      bubble: false
      verbosity_levels:
        VERBOSITY_VERBOSE: INFO
        VERBOSITY_VERY_VERBOSE: DEBUG
      channels: [ "!doctrine" ]
    console_very_verbose:
      type: console
      bubble: false
      verbosity_levels:
        VERBOSITY_VERBOSE: NOTICE
        VERBOSITY_VERY_VERBOSE: NOTICE
        VERBOSITY_DEBUG: DEBUG
      channels: [ "doctrine" ]
