imports:
  - { resource: config.yml }
  - { resource: security_dev.yml }

framework:
  router:
    resource: "%kernel.root_dir%/config/routing_dev.yml"
    strict_requirements: true
  profiler: { only_exceptions: false }

web_profiler:
  toolbar: '%use_debug_toolbar%'
  intercept_redirects: false

monolog:
  handlers:
    main:
      type: rotating_file
      max_files: '%env(PS_LOG_MAX_FILES)%'
      path: '%env(PS_LOG_OUTPUT)%'
      level: debug
      channels: [ "!event" ]
    console:
      type: console
      bubble: false
      verbosity_levels:
        VERBOSITY_VERBOSE: INFO
        VERBOSITY_VERY_VERBOSE: DEBUG
      channels: [ "!doctrine" ]
    console_very_verbose:
      type: console
      bubble: false
      verbosity_levels:
        VERBOSITY_VERBOSE: NOTICE
        VERBOSITY_VERY_VERBOSE: NOTICE
        VERBOSITY_DEBUG: DEBUG
      channels: [ "doctrine" ]
      # uncomment to get logging in your browser
      # you may have to allow bigger header sizes in your Web server configuration
      # firephp:
      #  type:   firephp
      #  level:  info
      # chromephp:
      #  type:   chromephp
      #  level:  info

# swiftmailer:
#  delivery_address: <EMAIL>

prestashop:
  addons:
    api_client:
      ttl: 300  # 5min

tactician:
  commandbus:
    default:
      middleware:
        - prestashop.core.command_bus.middleware.command_register_middleware
        - tactician.middleware.command_handler
