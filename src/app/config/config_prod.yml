imports:
  - { resource: config.yml }
  - { resource: security_prod.yml }

# framework:
#  validation:
#    cache: validator.mapping.cache.apc
#  serializer:
#    cache: serializer.mapping.cache.apc

# doctrine:
#  orm:
#    metadata_cache_driver: apc
#    result_cache_driver: apc
#    query_cache_driver: apc

monolog:
  handlers:
    main:
      type: fingers_crossed
      action_level: error
      handler: nested
    nested:
      type: rotating_file
      max_files: '%env(PS_LOG_MAX_FILES)%'
      path: '%env(PS_LOG_OUTPUT)%'
      level: debug
    console:
      type: console

doctrine:
  orm:
    metadata_cache_driver:
      type: pool
      pool: "%cache.driver%"
    query_cache_driver:
      type: pool
      pool: "%cache.driver%"
    result_cache_driver:
      type: pool
      pool: "%cache.driver%"
