imports:
  - { resource: set_parameters.php }
  - { resource: services.yml }
  - { resource: addons/*.yml }
  - { resource: doctrine.yml }

# Put parameters here that don't need to change on each machine where the app is deployed
# http://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
  env(PS_THEME_NAME): "classic"
  AdapterSecurityAdminClass: PrestaShop\PrestaShop\Adapter\Security\Admin
  translator.class: PrestaShopBundle\Translation\Translator
  translator.data_collector: PrestaShopBundle\Translation\DataCollectorTranslator
  prestashop_views: "%kernel.root_dir%/../src/PrestaShopBundle/Resources/views"
  admin_page: "%prestashop_views%/Admin"
  env(PS_LOG_OUTPUT): "%kernel.logs_dir%/%kernel.environment%.log"
  env(PS_LOG_MAX_FILES): 30
  mail_themes_uri: "/mails/themes"
  mail_themes_dir: "%kernel.project_dir%%mail_themes_uri%"
  modules_translation_paths: [ ]
  api_base_path: !php/const PrestaShopBundle\Api\Api::API_BASE_PATH

# Autowires Core controllers
services:
  logger:
    alias: monolog.logger
    public: true

framework:
  assets:
    version: !php/const PrestaShop\PrestaShop\Core\Version::VERSION

  # esi: ~
  secret: "%secret%"
  translator:
    fallbacks: [ "default" ]
    paths: "%modules_translation_paths%"
  router:
    resource: "%kernel.root_dir%/config/routing.yml"
    strict_requirements: ~
  form: ~
  csrf_protection: ~
  validation: { enable_annotations: true }
  serializer: { enable_annotations: true }
  default_locale: "%locale%"
  trusted_hosts: ~
  session:
    handler_id: ~
  fragments: ~
  http_method_override: true
  http_client: ~
  cache:
    pools:
      '%cache.driver%':
        adapter: '%cache.adapter%'

# Monolog configuration
monolog:
  handlers:
    main:
      type: rotating_file
      max_files: '%env(PS_LOG_MAX_FILES)%'
      path: '%env(PS_LOG_OUTPUT)%'
      level: notice
    legacy:
      type: service
      id: prestashop.handler.log
      level: warning
      channels: [ app ]

# Twig Configuration
twig:
  autoescape: "name"
  debug: "%kernel.debug%"
  strict_variables: "%kernel.debug%"
  exception_controller: null
  form_themes:
    - '@PrestaShop/Admin/TwigTemplateForm/bootstrap_4_horizontal_layout.html.twig'
  paths:
    '%admin_page%/Product': Product
    '%admin_page%/TwigTemplateForm': Twig
    '%admin_page%/Common': Common
    '%admin_page%/Configure/AdvancedParameters': AdvancedParameters
    '%admin_page%/Configure/ShopParameters': ShopParameters
    '%kernel.root_dir%/../modules': Modules
    '%mail_themes_dir%': MailThemes
    '%prestashop_views%': PrestaShopCore
  globals:
    webpack_server: false
    multistore_field_prefix: !php/const PrestaShopBundle\Service\Form\MultistoreCheckboxEnabler::MULTISTORE_FIELD_PREFIX
    modify_all_shops_prefix: !php/const PrestaShopBundle\Form\Admin\Extension\ModifyAllShopsExtension::MODIFY_ALL_SHOPS_PREFIX
    disabling_switch_prefix: !php/const PrestaShopBundle\Form\Admin\Extension\DisablingSwitchExtension::FIELD_PREFIX

# Swiftmailer Configuration
swiftmailer:
  transport: "%mailer_transport%"
  host: "%mailer_host%"
  username: "%mailer_user%"
  password: "%mailer_password%"
  spool: { type: memory }

api_platform:
  title: PrestaShop API
  version: 1.0.0
  enable_entrypoint: false
  mapping:
    paths:
      - '%kernel.project_dir%/app/config/api_platform'

  metadata_backward_compatibility_layer: false
