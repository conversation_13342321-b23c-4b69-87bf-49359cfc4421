# To get started with security, check out the documentation:
# https://symfony.com/doc/current/security.html
security:

  # https://symfony.com/doc/current/security.html#where-do-users-come-from-user-providers
  providers:
    in_memory:
      memory: ~
    admin:
      id: prestashop.security.admin.provider

    # Empty provider until a real provider is developed
    oauth2:
      memory:
        users:

  firewalls:
    # disables authentication for assets and the profiler, adapt it according to your needs
    dev:
      pattern: ^/(_(profiler|wdt)|css|images|js)/
      security: false

    main:
      anonymous: ~
