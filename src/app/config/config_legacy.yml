# This config file is destined for legacy containers built by PrestaShop core
# For now it is mainly used for Doctrine configuration, but in the future it could contain other extensions config
imports:
  - { resource: doctrine.yml }

doctrine:
  orm:
    mappings:
      # In front container we must define the mapping manually because PrestaShopBundle cannot do it
      PrestaShopBundle\Entity:
        type: annotation
        dir: "%kernel.project_dir%/src/PrestaShopBundle/Entity"
        is_bundle: false
        prefix: PrestaShop
