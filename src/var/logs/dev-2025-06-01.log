[2025-06-01 05:21:41] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:41] php.INFO: User Deprecated: The "PrestaShop\PrestaShop\Adapter\StockManager" class implements "PrestaShopBundle\Service\DataProvider\StockInterface" that is deprecated since 8.1 and will be removed in next major. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShop\\PrestaShop\\Adapter\\StockManager\" class implements \"PrestaShopBundle\\Service\\DataProvider\\StockInterface\" that is deprecated since 8.1 and will be removed in next major. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:41] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:41] php.INFO: User Deprecated: The "sensio_framework_extra.routing.loader.annot_class" service is deprecated since version 5.2 {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"sensio_framework_extra.routing.loader.annot_class\" service is deprecated since version 5.2 at /baora/var/cache/dev/ContainerX0HNdln/getSensioFrameworkExtra_Routing_Loader_AnnotClassService.php:9)"} []
[2025-06-01 05:21:41] php.INFO: User Deprecated: The "Sensio\Bundle\FrameworkExtraBundle\Routing\AnnotatedRouteControllerLoader" class is deprecated since version 5.2. Use "Symfony\Bundle\FrameworkBundle\Routing\AnnotatedRouteControllerLoader" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"Sensio\\Bundle\\FrameworkExtraBundle\\Routing\\AnnotatedRouteControllerLoader\" class is deprecated since version 5.2. Use \"Symfony\\Bundle\\FrameworkBundle\\Routing\\AnnotatedRouteControllerLoader\" instead. at /baora/vendor/sensio/framework-extra-bundle/src/Routing/AnnotatedRouteControllerLoader.php:19)"} []
[2025-06-01 05:21:41] php.INFO: User Deprecated: The "sensio_framework_extra.routing.loader.annot_dir" service is deprecated since version 5.2 {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"sensio_framework_extra.routing.loader.annot_dir\" service is deprecated since version 5.2 at /baora/var/cache/dev/ContainerX0HNdln/getSensioFrameworkExtra_Routing_Loader_AnnotDirService.php:9)"} []
[2025-06-01 05:21:41] php.INFO: User Deprecated: The "sensio_framework_extra.routing.loader.annot_file" service is deprecated since version 5.2 {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"sensio_framework_extra.routing.loader.annot_file\" service is deprecated since version 5.2 at /baora/var/cache/dev/ContainerX0HNdln/getSensioFrameworkExtra_Routing_Loader_AnnotFileService.php:9)"} []
[2025-06-01 05:21:42] php.INFO: User Deprecated: Referencing controllers with PrestaShopBundle:Admin/Sell/Catalog/Manufacturer:deleteLogoImage is deprecated since Symfony 4.1, use "PrestaShopBundle\Controller\Admin\Sell\Catalog\ManufacturerController::deleteLogoImageAction" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Referencing controllers with PrestaShopBundle:Admin/Sell/Catalog/Manufacturer:deleteLogoImage is deprecated since Symfony 4.1, use \"PrestaShopBundle\\Controller\\Admin\\Sell\\Catalog\\ManufacturerController::deleteLogoImageAction\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Routing/DelegatingLoader.php:108)"} []
[2025-06-01 05:21:42] php.INFO: User Deprecated: Referencing controllers with PrestaShopBundle:Admin/Sell/CustomerService/MerchandiseReturn:edit is deprecated since Symfony 4.1, use "PrestaShopBundle\Controller\Admin\Sell\CustomerService\MerchandiseReturnController::editAction" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Referencing controllers with PrestaShopBundle:Admin/Sell/CustomerService/MerchandiseReturn:edit is deprecated since Symfony 4.1, use \"PrestaShopBundle\\Controller\\Admin\\Sell\\CustomerService\\MerchandiseReturnController::editAction\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Routing/DelegatingLoader.php:108)"} []
[2025-06-01 05:21:42] php.INFO: User Deprecated: Referencing controllers with PrestaShopBundle:Admin\Configure\AdvancedParameters\Permission:index is deprecated since Symfony 4.1, use "PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\PermissionController::indexAction" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Referencing controllers with PrestaShopBundle:Admin\\Configure\\AdvancedParameters\\Permission:index is deprecated since Symfony 4.1, use \"PrestaShopBundle\\Controller\\Admin\\Configure\\AdvancedParameters\\PermissionController::indexAction\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Routing/DelegatingLoader.php:108)"} []
[2025-06-01 05:21:42] php.INFO: User Deprecated: Referencing controllers with PrestaShopBundle:Admin\Configure\AdvancedParameters\Permission:updateTabPermissions is deprecated since Symfony 4.1, use "PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\PermissionController::updateTabPermissionsAction" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Referencing controllers with PrestaShopBundle:Admin\\Configure\\AdvancedParameters\\Permission:updateTabPermissions is deprecated since Symfony 4.1, use \"PrestaShopBundle\\Controller\\Admin\\Configure\\AdvancedParameters\\PermissionController::updateTabPermissionsAction\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Routing/DelegatingLoader.php:108)"} []
[2025-06-01 05:21:42] php.INFO: User Deprecated: Referencing controllers with PrestaShopBundle:Admin\Configure\AdvancedParameters\Permission:updateModulePermissions is deprecated since Symfony 4.1, use "PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\PermissionController::updateModulePermissionsAction" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Referencing controllers with PrestaShopBundle:Admin\\Configure\\AdvancedParameters\\Permission:updateModulePermissions is deprecated since Symfony 4.1, use \"PrestaShopBundle\\Controller\\Admin\\Configure\\AdvancedParameters\\PermissionController::updateModulePermissionsAction\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Routing/DelegatingLoader.php:108)"} []
[2025-06-01 05:21:42] php.INFO: User Deprecated: Referencing controllers with PrestaShopBundle:Admin\Common:searchGrid is deprecated since Symfony 4.1, use "PrestaShopBundle\Controller\Admin\CommonController::searchGridAction" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Referencing controllers with PrestaShopBundle:Admin\\Common:searchGrid is deprecated since Symfony 4.1, use \"PrestaShopBundle\\Controller\\Admin\\CommonController::searchGridAction\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Routing/DelegatingLoader.php:108)"} []
[2025-06-01 05:21:42] doctrine.DEBUG: SELECT t0.id_feature_flag AS id_feature_flag_1, t0.name AS name_2, t0.state AS state_3, t0.label_wording AS label_wording_4, t0.label_domain AS label_domain_5, t0.description_wording AS description_wording_6, t0.description_domain AS description_domain_7, t0.stability AS stability_8 FROM feature_flag t0 WHERE t0.name = ? LIMIT 1 ["product_page_v2"] []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:43] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:43] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:43] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:21:43] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:21:43] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=1748769862377","method":"POST"} []
[2025-06-01 05:21:43] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:21:43] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:21:43] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:21:43] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:21:43] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:43] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:21:43] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:21:43] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:21:43] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:21:43] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:21:43] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:43] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:21:46] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:46] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:46] request.INFO: Matched route "admin_module_manage". {"route":"admin_module_manage","route_parameters":{"_route":"admin_module_manage","category":null,"keyword":null,"_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\ModuleController::manageAction","_legacy_controller":"AdminModulesManage","_legacy_link":["AdminModulesManage","AdminModulesSf"]},"request_uri":"http://baora.home.local/backstage/index.php/improve/modules/manage?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:21:46] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:21:46] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:21:46] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:21:46] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:46] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:51] php.INFO: Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated at /baora/modules/ps_imageslider/ps_imageslider.php:55)"} []
[2025-06-01 05:21:52] php.INFO: Deprecated: Creation of dynamic property mpm_blog::$_objPost is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property mpm_blog::$_objPost is deprecated at /baora/modules/mpm_blog/mpm_blog.php:24)"} []
[2025-06-01 05:21:52] php.INFO: Deprecated: Creation of dynamic property mpm_productinfo::$templateFile is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property mpm_productinfo::$templateFile is deprecated at /baora/modules/mpm_productinfo/mpm_productinfo.php:27)"} []
[2025-06-01 05:21:52] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:21:52] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: Twig Filter "configuration" is deprecated in /baora/src/PrestaShopBundle/Resources/views/Admin/Module/common.html.twig at line 48. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Twig Filter \"configuration\" is deprecated in /baora/src/PrestaShopBundle/Resources/views/Admin/Module/common.html.twig at line 48. at /baora/vendor/twig/twig/src/ExpressionParser.php:802)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The "prestashop.core.admin.feature_flag.repository" service alias is deprecated. You should stop using it, as it will be removed in the future. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"prestashop.core.admin.feature_flag.repository\" service alias is deprecated. You should stop using it, as it will be removed in the future. at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:5276)"} []
[2025-06-01 05:21:52] doctrine.DEBUG: SELECT t0.id_feature_flag AS id_feature_flag_1, t0.name AS name_2, t0.state AS state_3, t0.label_wording AS label_wording_4, t0.label_domain AS label_domain_5, t0.description_wording AS description_wording_6, t0.description_domain AS description_domain_7, t0.stability AS stability_8 FROM feature_flag t0 WHERE t0.name = ? LIMIT 1 ["product_page_v2"] []
[2025-06-01 05:21:52] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:21:52] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:21:52] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:21:53] php.WARNING: Uncaught Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: The "Symfony\Component\HttpKernel\Event\GetResponseForExceptionEvent::getException()" method is deprecated since Symfony 4.4, use "getThrowable()" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"Symfony\\Component\\HttpKernel\\Event\\GetResponseForExceptionEvent::getException()\" method is deprecated since Symfony 4.4, use \"getThrowable()\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/Event/GetResponseForExceptionEvent.php:57)"} []
[2025-06-01 05:21:53] request.CRITICAL: Uncaught PHP Exception ErrorException: "Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking" at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php line 737 {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:53] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:53] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:53] request.INFO: Matched route "_wdt". {"route":"_wdt","route_parameters":{"_route":"_wdt","_controller":"web_profiler.controller.profiler::toolbarAction","token":"223347"},"request_uri":"http://baora.home.local/backstage/index.php/_wdt/223347?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:21:53] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:21:53] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:21:53] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:21:53] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:21:53] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:53] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:53] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=1748769872861","method":"POST"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:53] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:21:53] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:53] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:54] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:21:54] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:54] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:21:54] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:54] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:21:54] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:21:54] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:21:54] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:21:54] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:54] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: Implementing the "PrestaShopBundle\DataCollector\HookDataCollector::unserialize()" method is deprecated since Symfony 4.3, store all the serialized state in the "data" property instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Implementing the \"PrestaShopBundle\\DataCollector\\HookDataCollector::unserialize()\" method is deprecated since Symfony 4.3, store all the serialized state in the \"data\" property instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/DataCollector.php:130)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:21:54] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:21:54] request.INFO: Matched route "admin_module_notification_count". {"route":"admin_module_notification_count","route_parameters":{"_route":"admin_module_notification_count","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\Modules\\AlertsController::notificationsCountAction","_legacy_controller":"AdminModulesNotifications","_legacy_link":"AdminModulesNotifications:count"},"request_uri":"http://baora.home.local/backstage/index.php/improve/modules/alerts/count?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:21:54] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationName()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationName()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:118)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:21:54] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:21:54] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:21:54] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:21:55] php.INFO: Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated at /baora/modules/ps_imageslider/ps_imageslider.php:55)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:21:55] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:21:55] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:21:55] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:21:55] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:13] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:13] request.INFO: Matched route "admin_module_manage_action". {"route":"admin_module_manage_action","route_parameters":{"_route":"admin_module_manage_action","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\ModuleController::moduleAction","action":"disable","module_name":"ht_brandlist"},"request_uri":"http://baora.home.local/backstage/index.php/improve/modules/manage/action/disable/ht_brandlist?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:22:13] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:13] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:13] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:13] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_brandlist"] []
[2025-06-01 05:22:13] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:13] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:13] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:13] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:27] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:27] request.INFO: Matched route "admin_themes_index". {"route":"admin_themes_index","route_parameters":{"_route":"admin_themes_index","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\Design\\ThemeController::indexAction","_legacy_controller":"AdminThemes","_legacy_link":"AdminThemes"},"request_uri":"http://baora.home.local/backstage/index.php/improve/design/themes/?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:22:27] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:27] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:27] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:27] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:27] php.INFO: User Deprecated: The "prestashop.core.admin.feature_flag.repository" service alias is deprecated. You should stop using it, as it will be removed in the future. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"prestashop.core.admin.feature_flag.repository\" service alias is deprecated. You should stop using it, as it will be removed in the future. at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:5276)"} []
[2025-06-01 05:22:27] doctrine.DEBUG: SELECT t0.id_feature_flag AS id_feature_flag_1, t0.name AS name_2, t0.state AS state_3, t0.label_wording AS label_wording_4, t0.label_domain AS label_domain_5, t0.description_wording AS description_wording_6, t0.description_domain AS description_domain_7, t0.stability AS stability_8 FROM feature_flag t0 WHERE t0.name = ? LIMIT 1 ["product_page_v2"] []
[2025-06-01 05:22:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: Twig Filter "transchoice" is deprecated since version 4.2. Use "trans" with parameter "%count%" instead in /baora/src/PrestaShopBundle/Resources/views/Admin/TwigTemplateForm/bootstrap_4_layout.html.twig at line 559. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Twig Filter \"transchoice\" is deprecated since version 4.2. Use \"trans\" with parameter \"%count%\" instead in /baora/src/PrestaShopBundle/Resources/views/Admin/TwigTemplateForm/bootstrap_4_layout.html.twig at line 559. at /baora/vendor/twig/twig/src/ExpressionParser.php:802)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: Twig Filter "transchoice" is deprecated since version 4.2. Use "trans" with parameter "%count%" instead in /baora/src/PrestaShopBundle/Resources/views/Admin/TwigTemplateForm/bootstrap_4_layout.html.twig at line 570. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Twig Filter \"transchoice\" is deprecated since version 4.2. Use \"trans\" with parameter \"%count%\" instead in /baora/src/PrestaShopBundle/Resources/views/Admin/TwigTemplateForm/bootstrap_4_layout.html.twig at line 570. at /baora/vendor/twig/twig/src/ExpressionParser.php:802)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:28] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:28] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:29] php.WARNING: Uncaught Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: The "Symfony\Component\HttpKernel\Event\GetResponseForExceptionEvent::getException()" method is deprecated since Symfony 4.4, use "getThrowable()" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"Symfony\\Component\\HttpKernel\\Event\\GetResponseForExceptionEvent::getException()\" method is deprecated since Symfony 4.4, use \"getThrowable()\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/Event/GetResponseForExceptionEvent.php:57)"} []
[2025-06-01 05:22:29] request.CRITICAL: Uncaught PHP Exception ErrorException: "Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking" at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php line 737 {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:29] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:29] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:29] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:22:29] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:29] request.INFO: Matched route "_wdt". {"route":"_wdt","route_parameters":{"_route":"_wdt","_controller":"web_profiler.controller.profiler::toolbarAction","token":"9b91fb"},"request_uri":"http://baora.home.local/backstage/index.php/_wdt/9b91fb?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:22:29] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:29] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:29] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:29] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:29] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:29] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:29] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:22:29] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:29] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:29] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:29] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:29] php.INFO: User Deprecated: Implementing the "PrestaShopBundle\DataCollector\HookDataCollector::unserialize()" method is deprecated since Symfony 4.3, store all the serialized state in the "data" property instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Implementing the \"PrestaShopBundle\\DataCollector\\HookDataCollector::unserialize()\" method is deprecated since Symfony 4.3, store all the serialized state in the \"data\" property instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/DataCollector.php:130)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationName()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationName()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:118)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:30] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:30] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:30] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:30] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:30] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:30] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:41] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:41] request.INFO: Matched route "admin_theme_customize_layouts". {"route":"admin_theme_customize_layouts","route_parameters":{"_route":"admin_theme_customize_layouts","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\Design\\ThemeController::customizeLayoutsAction","_legacy_controller":"AdminThemes","_legacy_link":"AdminThemes:submitConfigureLayouts"},"request_uri":"http://baora.home.local/backstage/index.php/improve/design/themes/customize-layouts?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:22:41] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:41] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:41] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:41] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:41] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The "prestashop.core.admin.feature_flag.repository" service alias is deprecated. You should stop using it, as it will be removed in the future. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"prestashop.core.admin.feature_flag.repository\" service alias is deprecated. You should stop using it, as it will be removed in the future. at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:5276)"} []
[2025-06-01 05:22:41] doctrine.DEBUG: SELECT t0.id_feature_flag AS id_feature_flag_1, t0.name AS name_2, t0.state AS state_3, t0.label_wording AS label_wording_4, t0.label_domain AS label_domain_5, t0.description_wording AS description_wording_6, t0.description_domain AS description_domain_7, t0.stability AS stability_8 FROM feature_flag t0 WHERE t0.name = ? LIMIT 1 ["product_page_v2"] []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:41] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:41] php.WARNING: Uncaught Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:22:41] php.INFO: User Deprecated: The "Symfony\Component\HttpKernel\Event\GetResponseForExceptionEvent::getException()" method is deprecated since Symfony 4.4, use "getThrowable()" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"Symfony\\Component\\HttpKernel\\Event\\GetResponseForExceptionEvent::getException()\" method is deprecated since Symfony 4.4, use \"getThrowable()\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/Event/GetResponseForExceptionEvent.php:57)"} []
[2025-06-01 05:22:41] request.CRITICAL: Uncaught PHP Exception ErrorException: "Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking" at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php line 737 {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:42] request.INFO: Matched route "_wdt". {"route":"_wdt","route_parameters":{"_route":"_wdt","_controller":"web_profiler.controller.profiler::toolbarAction","token":"4b3085"},"request_uri":"http://baora.home.local/backstage/index.php/_wdt/4b3085?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:22:42] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:42] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:22:42] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:42] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:42] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:42] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:42] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:22:42] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Implementing the "PrestaShopBundle\DataCollector\HookDataCollector::unserialize()" method is deprecated since Symfony 4.3, store all the serialized state in the "data" property instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Implementing the \"PrestaShopBundle\\DataCollector\\HookDataCollector::unserialize()\" method is deprecated since Symfony 4.3, store all the serialized state in the \"data\" property instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/DataCollector.php:130)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationName()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationName()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:118)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:42] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:42] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:42] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:42] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:42] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:55] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:55] request.INFO: Matched route "admin_themes_enable". {"route":"admin_themes_enable","route_parameters":{"_route":"admin_themes_enable","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\Design\\ThemeController::enableAction","_legacy_controller":"AdminThemes","_legacy_link":"AdminThemes:enableTheme","_legacy_parameters":{"theme_name":"themeName"},"themeName":"lilac-new"},"request_uri":"http://baora.home.local/backstage/index.php/improve/design/themes/lilac-new/enable?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:22:55] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:55] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:55] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: prestashop.adapter.legacy_db service is deprecated and will be removed in 8.0. {"exception":"[object] (ErrorException(code: 0): User Deprecated: prestashop.adapter.legacy_db service is deprecated and will be removed in 8.0. at /baora/var/cache/dev/ContainerX0HNdln/getPrestashop_Adapter_LegacyDbService.php:9)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:55] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ps_newproducts"] []
[2025-06-01 05:22:55] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ps_socialfollow"] []
[2025-06-01 05:22:55] php.INFO: Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated at /baora/modules/ps_imageslider/ps_imageslider.php:55)"} []
[2025-06-01 05:22:55] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ps_imageslider"] []
[2025-06-01 05:22:55] doctrine.DEBUG: "START TRANSACTION" [] []
[2025-06-01 05:22:55] doctrine.DEBUG: UPDATE tab SET enabled = ? WHERE id_tab = ? [false,125] []
[2025-06-01 05:22:55] doctrine.DEBUG: "COMMIT" [] []
[2025-06-01 05:22:55] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_staticblocks"] []
[2025-06-01 05:22:55] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_scrolltop"] []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_googleanalytics"] []
[2025-06-01 05:22:56] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:56] app.INFO: Protect vendor folder in module ps_newproducts [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ps_newproducts"] []
[2025-06-01 05:22:56] app.INFO: Protect vendor folder in module ps_socialfollow [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] app.INFO: Module ps_socialfollow has no vendor folder [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ps_socialfollow"] []
[2025-06-01 05:22:56] app.INFO: Protect vendor folder in module ps_imageslider [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] app.INFO: Module ps_imageslider has no vendor folder [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ps_imageslider"] []
[2025-06-01 05:22:56] doctrine.DEBUG: "START TRANSACTION" [] []
[2025-06-01 05:22:56] doctrine.DEBUG: UPDATE tab SET enabled = ? WHERE id_tab = ? [true,125] []
[2025-06-01 05:22:56] doctrine.DEBUG: "COMMIT" [] []
[2025-06-01 05:22:56] app.INFO: Protect vendor folder in module ht_scrolltop [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] app.INFO: Module ht_scrolltop has no vendor folder [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_scrolltop"] []
[2025-06-01 05:22:56] app.INFO: Protect vendor folder in module ht_staticblocks [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] app.INFO: Module ht_staticblocks has no vendor folder [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_staticblocks"] []
[2025-06-01 05:22:56] app.INFO: Protect vendor folder in module ht_brandlist [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] app.INFO: Module ht_brandlist has no vendor folder [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_brandlist"] []
[2025-06-01 05:22:56] app.INFO: Protect vendor folder in module ht_googleanalytics [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] app.INFO: Module ht_googleanalytics has no vendor folder [] []
[2025-06-01 05:22:56] php.INFO: Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopLogger::$hash is deprecated at /baora/classes/PrestaShopLogger.php:194)"} []
[2025-06-01 05:22:56] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_googleanalytics"] []
[2025-06-01 05:22:57] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:57] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:57] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:57] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:57] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:57] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:57] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:57] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:57] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:57] request.INFO: Matched route "admin_themes_index". {"route":"admin_themes_index","route_parameters":{"_route":"admin_themes_index","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\Design\\ThemeController::indexAction","_legacy_controller":"AdminThemes","_legacy_link":"AdminThemes"},"request_uri":"http://baora.home.local/backstage/index.php/improve/design/themes/?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:22:57] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:57] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:57] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:57] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:58] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The "prestashop.core.admin.feature_flag.repository" service alias is deprecated. You should stop using it, as it will be removed in the future. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"prestashop.core.admin.feature_flag.repository\" service alias is deprecated. You should stop using it, as it will be removed in the future. at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:5276)"} []
[2025-06-01 05:22:58] doctrine.DEBUG: SELECT t0.id_feature_flag AS id_feature_flag_1, t0.name AS name_2, t0.state AS state_3, t0.label_wording AS label_wording_4, t0.label_domain AS label_domain_5, t0.description_wording AS description_wording_6, t0.description_domain AS description_domain_7, t0.stability AS stability_8 FROM feature_flag t0 WHERE t0.name = ? LIMIT 1 ["product_page_v2"] []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:58] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:58] php.WARNING: Uncaught Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:22:58] php.INFO: User Deprecated: The "Symfony\Component\HttpKernel\Event\GetResponseForExceptionEvent::getException()" method is deprecated since Symfony 4.4, use "getThrowable()" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"Symfony\\Component\\HttpKernel\\Event\\GetResponseForExceptionEvent::getException()\" method is deprecated since Symfony 4.4, use \"getThrowable()\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/Event/GetResponseForExceptionEvent.php:57)"} []
[2025-06-01 05:22:58] request.CRITICAL: Uncaught PHP Exception ErrorException: "Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking" at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php line 737 {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:59] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:22:59] request.INFO: Matched route "_wdt". {"route":"_wdt","route_parameters":{"_route":"_wdt","_controller":"web_profiler.controller.profiler::toolbarAction","token":"c22d29"},"request_uri":"http://baora.home.local/backstage/index.php/_wdt/c22d29?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:22:59] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:59] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:59] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:59] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:22:59] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:59] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:22:59] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Implementing the "PrestaShopBundle\DataCollector\HookDataCollector::unserialize()" method is deprecated since Symfony 4.3, store all the serialized state in the "data" property instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Implementing the \"PrestaShopBundle\\DataCollector\\HookDataCollector::unserialize()\" method is deprecated since Symfony 4.3, store all the serialized state in the \"data\" property instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/DataCollector.php:130)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationName()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationName()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:118)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:22:59] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:22:59] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:22:59] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:22:59] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:22:59] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:15] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:15] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:15] request.INFO: Matched route "admin_module_manage". {"route":"admin_module_manage","route_parameters":{"_route":"admin_module_manage","category":null,"keyword":null,"_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\ModuleController::manageAction","_legacy_controller":"AdminModulesManage","_legacy_link":["AdminModulesManage","AdminModulesSf"]},"request_uri":"http://baora.home.local/backstage/index.php/improve/modules/manage?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:23:15] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:15] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:15] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:15] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:15] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:15] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:15] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:15] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:15] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:16] php.INFO: Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated at /baora/modules/ps_imageslider/ps_imageslider.php:55)"} []
[2025-06-01 05:23:17] php.INFO: Deprecated: Creation of dynamic property mpm_blog::$_objPost is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property mpm_blog::$_objPost is deprecated at /baora/modules/mpm_blog/mpm_blog.php:24)"} []
[2025-06-01 05:23:17] php.INFO: Deprecated: Creation of dynamic property mpm_productinfo::$templateFile is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property mpm_productinfo::$templateFile is deprecated at /baora/modules/mpm_productinfo/mpm_productinfo.php:27)"} []
[2025-06-01 05:23:17] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:17] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The "prestashop.core.admin.feature_flag.repository" service alias is deprecated. You should stop using it, as it will be removed in the future. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"prestashop.core.admin.feature_flag.repository\" service alias is deprecated. You should stop using it, as it will be removed in the future. at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:5276)"} []
[2025-06-01 05:23:17] doctrine.DEBUG: SELECT t0.id_feature_flag AS id_feature_flag_1, t0.name AS name_2, t0.state AS state_3, t0.label_wording AS label_wording_4, t0.label_domain AS label_domain_5, t0.description_wording AS description_wording_6, t0.description_domain AS description_domain_7, t0.stability AS stability_8 FROM feature_flag t0 WHERE t0.name = ? LIMIT 1 ["product_page_v2"] []
[2025-06-01 05:23:17] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:17] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:17] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:18] php.WARNING: Uncaught Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: The "Symfony\Component\HttpKernel\Event\GetResponseForExceptionEvent::getException()" method is deprecated since Symfony 4.4, use "getThrowable()" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"Symfony\\Component\\HttpKernel\\Event\\GetResponseForExceptionEvent::getException()\" method is deprecated since Symfony 4.4, use \"getThrowable()\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/Event/GetResponseForExceptionEvent.php:57)"} []
[2025-06-01 05:23:18] request.CRITICAL: Uncaught PHP Exception ErrorException: "Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking" at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php line 737 {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:18] request.INFO: Matched route "_wdt". {"route":"_wdt","route_parameters":{"_route":"_wdt","_controller":"web_profiler.controller.profiler::toolbarAction","token":"e378bd"},"request_uri":"http://baora.home.local/backstage/index.php/_wdt/e378bd?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:23:18] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:18] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:18] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:23:18] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:18] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=1748769957651","method":"POST"} []
[2025-06-01 05:23:18] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:18] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:18] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:18] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:18] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:18] request.INFO: Matched route "admin_module_notification_count". {"route":"admin_module_notification_count","route_parameters":{"_route":"admin_module_notification_count","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\Modules\\AlertsController::notificationsCountAction","_legacy_controller":"AdminModulesNotifications","_legacy_link":"AdminModulesNotifications:count"},"request_uri":"http://baora.home.local/backstage/index.php/improve/modules/alerts/count?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:23:18] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:18] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:18] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:18] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:19] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:19] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:19] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:19] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:19] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:19] php.INFO: Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property Ps_ImageSlider::$secure_key is deprecated at /baora/modules/ps_imageslider/ps_imageslider.php:55)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:19] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:19] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:19] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: Implementing the "PrestaShopBundle\DataCollector\HookDataCollector::unserialize()" method is deprecated since Symfony 4.3, store all the serialized state in the "data" property instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Implementing the \"PrestaShopBundle\\DataCollector\\HookDataCollector::unserialize()\" method is deprecated since Symfony 4.3, store all the serialized state in the \"data\" property instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/DataCollector.php:130)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationName()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationName()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:118)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:19] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:28] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:28] request.INFO: Matched route "admin_module_manage_action". {"route":"admin_module_manage_action","route_parameters":{"_route":"admin_module_manage_action","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\ModuleController::moduleAction","action":"disable","module_name":"ht_brandlist"},"request_uri":"http://baora.home.local/backstage/index.php/improve/modules/manage/action/disable/ht_brandlist?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:23:28] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:28] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:28] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:28] doctrine.DEBUG: SELECT t0.id_tab AS id_tab_1, t0.id_parent AS id_parent_2, t0.position AS position_3, t0.module AS module_4, t0.class_name AS class_name_5, t0.route_name AS route_name_6, t0.active AS active_7, t0.enabled AS enabled_8, t0.icon AS icon_9, t0.wording AS wording_10, t0.wording_domain AS wording_domain_11 FROM tab t0 WHERE t0.module = ? ["ht_brandlist"] []
[2025-06-01 05:23:28] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:28] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:28] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:28] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:43] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:43] request.INFO: Matched route "admin_themes_index". {"route":"admin_themes_index","route_parameters":{"_route":"admin_themes_index","_controller":"PrestaShopBundle\\Controller\\Admin\\Improve\\Design\\ThemeController::indexAction","_legacy_controller":"AdminThemes","_legacy_link":"AdminThemes"},"request_uri":"http://baora.home.local/backstage/index.php/improve/design/themes/?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:23:43] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:43] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:43] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:43] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The _raw parameter is deprecated and will be removed in the next major version. at /baora/classes/controller/Controller.php:350)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The "prestashop.core.admin.feature_flag.repository" service alias is deprecated. You should stop using it, as it will be removed in the future. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"prestashop.core.admin.feature_flag.repository\" service alias is deprecated. You should stop using it, as it will be removed in the future. at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:5276)"} []
[2025-06-01 05:23:43] doctrine.DEBUG: SELECT t0.id_feature_flag AS id_feature_flag_1, t0.name AS name_2, t0.state AS state_3, t0.label_wording AS label_wording_4, t0.label_domain AS label_domain_5, t0.description_wording AS description_wording_6, t0.description_domain AS description_domain_7, t0.stability AS stability_8 FROM feature_flag t0 WHERE t0.name = ? LIMIT 1 ["product_page_v2"] []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The legacy parameter is deprecated and will be removed in the next major version. at /baora/src/PrestaShopBundle/Translation/PrestaShopTranslatorTrait.php:53)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:43] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:43] php.WARNING: Uncaught Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:23:43] php.INFO: User Deprecated: The "Symfony\Component\HttpKernel\Event\GetResponseForExceptionEvent::getException()" method is deprecated since Symfony 4.4, use "getThrowable()" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"Symfony\\Component\\HttpKernel\\Event\\GetResponseForExceptionEvent::getException()\" method is deprecated since Symfony 4.4, use \"getThrowable()\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/Event/GetResponseForExceptionEvent.php:57)"} []
[2025-06-01 05:23:43] request.CRITICAL: Uncaught PHP Exception ErrorException: "Warning: Undefined property: Symfony\Component\VarDumper\Caster\CutStub::$cache_locking" at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php line 737 {"exception":"[object] (ErrorException(code: 0): Warning: Undefined property: Symfony\\Component\\VarDumper\\Caster\\CutStub::$cache_locking at /baora/vendor/smarty/smarty/libs/sysplugins/smarty_internal_template.php:737)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:44] request.INFO: Matched route "_wdt". {"route":"_wdt","route_parameters":{"_route":"_wdt","_controller":"web_profiler.controller.profiler::toolbarAction","token":"634e33"},"request_uri":"http://baora.home.local/backstage/index.php/_wdt/634e33?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"GET"} []
[2025-06-01 05:23:44] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:44] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:44] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:44] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:44] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:44] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:23:44] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:23:44] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:44] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Implementing the "PrestaShopBundle\DataCollector\HookDataCollector::unserialize()" method is deprecated since Symfony 4.3, store all the serialized state in the "data" property instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Implementing the \"PrestaShopBundle\\DataCollector\\HookDataCollector::unserialize()\" method is deprecated since Symfony 4.3, store all the serialized state in the \"data\" property instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/DataCollector.php:130)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationName()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationName()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:118)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::getApplicationVersion()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::getApplicationVersion()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:128)"} []
[2025-06-01 05:23:44] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:23:44] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:23:44] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:23:44] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:23:44] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:25:45] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:25:45] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:25:45] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:25:45] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:25:45] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:25:45] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:25:45] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:25:45] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:25:45] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:25:46] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:25:46] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:25:46] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:25:46] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:25:46] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:25:46] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:25:46] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:25:46] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:25:46] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:27:46] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:27:46] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:27:46] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:27:46] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:27:46] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:27:46] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:27:46] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:27:46] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:27:46] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:27:47] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:27:47] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:27:47] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:27:47] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:27:47] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:27:47] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:27:47] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:27:47] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:27:47] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:29:47] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:29:47] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U","method":"POST"} []
[2025-06-01 05:29:47] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:29:47] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:29:47] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:29:47] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:29:47] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:29:47] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:29:47] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: Since api-platform/core 2.7: The service "ApiPlatform\Core\Bridge\Symfony\Routing\IriConverter" is deprecated, use ApiPlatform\Symfony\Routing\IriConverter instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: Since api-platform/core 2.7: The service \"ApiPlatform\\Core\\Bridge\\Symfony\\Routing\\IriConverter\" is deprecated, use ApiPlatform\\Symfony\\Routing\\IriConverter instead. at /baora/vendor/symfony/deprecation-contracts/function.php:25)"} []
[2025-06-01 05:29:48] php.INFO: Deprecated: Using ${var} in strings is deprecated, use {$var} instead {"exception":"[object] (ErrorException(code: 0): Deprecated: Using ${var} in strings is deprecated, use {$var} instead at /baora/src/PrestaShopBundle/Security/Admin/EmployeeProvider.php:70)"} []
[2025-06-01 05:29:48] request.INFO: Matched route "admin_common_notifications". {"route":"admin_common_notifications","route_parameters":{"_route":"admin_common_notifications","_controller":"PrestaShopBundle\\Controller\\Admin\\CommonController::notificationsAction","_legacy_link":"AdminCommon"},"request_uri":"http://baora.home.local/backstage/index.php/common/notifications?_token=UHaHZd9mUi9TbCHpBc5NaE4i_dcVgnOY2k3hlE6Kl1U&rand=*************","method":"POST"} []
[2025-06-01 05:29:48] doctrine.DEBUG: SELECT t0.id_lang AS id_lang_1, t0.name AS name_2, t0.active AS active_3, t0.iso_code AS iso_code_4, t0.language_code AS language_code_5, t0.locale AS locale_6, t0.date_format_lite AS date_format_lite_7, t0.date_format_full AS date_format_full_8, t0.is_rtl AS is_rtl_9 FROM lang t0 WHERE t0.id_lang = ? ["1"] []
[2025-06-01 05:29:48] security.DEBUG: Read existing security token from the session. {"key":"_security_main","token_class":"Symfony\\Component\\Security\\Core\\Authentication\\Token\\UsernamePasswordToken"} []
[2025-06-01 05:29:48] security.DEBUG: User was reloaded from a user provider. {"provider":"PrestaShopBundle\\Security\\Admin\\EmployeeProvider","username":"<EMAIL>"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: The "PrestaShopBundle\Service\Hook\HookEvent" class extends "Symfony\Component\EventDispatcher\Event" that is deprecated since Symfony 4.3, use "Symfony\Contracts\EventDispatcher\Event" instead. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"PrestaShopBundle\\Service\\Hook\\HookEvent\" class extends \"Symfony\\Component\\EventDispatcher\\Event\" that is deprecated since Symfony 4.3, use \"Symfony\\Contracts\\EventDispatcher\\Event\" instead. at /baora/vendor/symfony/symfony/src/Symfony/Component/ErrorHandler/DebugClassLoader.php:392)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* {"exception":"[object] (ErrorException(code: 0): User Deprecated: Not specifying the optional ShopConstraint parameter is deprecated since version ******* at /baora/src/Adapter/Configuration.php:425)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: The "$name" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$name\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:37)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: The "$version" argument in method "Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector::__construct()" is deprecated since Symfony 4.2. {"exception":"[object] (ErrorException(code: 0): User Deprecated: The \"$version\" argument in method \"Symfony\\Component\\HttpKernel\\DataCollector\\ConfigDataCollector::__construct()\" is deprecated since Symfony 4.2. at /baora/vendor/symfony/symfony/src/Symfony/Component/HttpKernel/DataCollector/ConfigDataCollector.php:40)"} []
[2025-06-01 05:29:48] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$logger is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$logger is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3553)"} []
[2025-06-01 05:29:48] php.INFO: Deprecated: Creation of dynamic property PrestaShopBundle\Twig\ContextIsoCodeProviderExtension::$translator is deprecated {"exception":"[object] (ErrorException(code: 0): Deprecated: Creation of dynamic property PrestaShopBundle\\Twig\\ContextIsoCodeProviderExtension::$translator is deprecated at /baora/var/cache/dev/ContainerX0HNdln/appAppKernelDevDebugContainer.php:3554)"} []
[2025-06-01 05:29:48] php.INFO: User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. {"exception":"[object] (ErrorException(code: 0): User Deprecated: __construct is deprecated since version 8.1 and will be removed in the next major version. at /baora/src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php:70)"} []
[2025-06-01 05:29:48] security.DEBUG: Stored the security token in the session. {"key":"_security_main"} []
