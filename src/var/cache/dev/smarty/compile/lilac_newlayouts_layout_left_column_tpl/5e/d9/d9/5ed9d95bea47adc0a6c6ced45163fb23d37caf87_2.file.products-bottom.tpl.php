<?php
/* Smarty version 4.3.4, created on 2025-06-01 05:24:21
  from '/baora/themes/lilac-new/templates/catalog/_partials/products-bottom.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.3.4',
  'unifunc' => 'content_683c1c456227c6_71875816',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '5ed9d95bea47adc0a6c6ced45163fb23d37caf87' => 
    array (
      0 => '/baora/themes/lilac-new/templates/catalog/_partials/products-bottom.tpl',
      1 => 1748768050,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_683c1c456227c6_71875816 (Smarty_Internal_Template $_smarty_tpl) {
?><div id="js-product-list-bottom"></div>
<?php }
}
