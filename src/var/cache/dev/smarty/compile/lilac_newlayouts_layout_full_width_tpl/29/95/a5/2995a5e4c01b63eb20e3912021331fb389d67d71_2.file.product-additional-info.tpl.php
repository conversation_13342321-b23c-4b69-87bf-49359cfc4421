<?php
/* Smarty version 4.3.4, created on 2025-06-01 05:23:51
  from '/baora/themes/lilac-new/templates/catalog/_partials/product-additional-info.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.3.4',
  'unifunc' => 'content_683c1c27003da6_25778511',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '2995a5e4c01b63eb20e3912021331fb389d67d71' => 
    array (
      0 => '/baora/themes/lilac-new/templates/catalog/_partials/product-additional-info.tpl',
      1 => 1748768050,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_683c1c27003da6_25778511 (Smarty_Internal_Template $_smarty_tpl) {
?><div class="product-additional-info">
  <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['hook'][0], array( array('h'=>'displayProductAdditionalInfo','product'=>$_smarty_tpl->tpl_vars['product']->value),$_smarty_tpl ) );?>

</div>
<?php }
}
