<?php
/* Smarty version 4.3.4, created on 2025-06-01 05:23:50
  from '/baora/themes/lilac-new/templates/catalog/_partials/product-add-to-cart.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.3.4',
  'unifunc' => 'content_683c1c26f40961_07669788',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '0cedba86d4dceb66935b5a4c17b77cd7b445ddc5' => 
    array (
      0 => '/baora/themes/lilac-new/templates/catalog/_partials/product-add-to-cart.tpl',
      1 => 1748768050,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_683c1c26f40961_07669788 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_loadInheritance();
$_smarty_tpl->inheritance->init($_smarty_tpl, false);
?>

<?php if (Group::getCurrent()->id != 4) {?>
    <div class="product-add-to-cart">
        <?php if (!$_smarty_tpl->tpl_vars['configuration']->value['is_catalog']) {?>
            <span class="control-label"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['l'][0], array( array('s'=>'Quantity','d'=>'Shop.Theme.Catalog'),$_smarty_tpl ) );?>
</span>

            <?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_1219215250683c1c26f35666_24451567', 'product_quantity');
?>


            <?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_1938659794683c1c26f394c2_08097154', 'product_availability');
?>


            <?php 
$_smarty_tpl->inheritance->instanceBlock($_smarty_tpl, 'Block_1167677570683c1c26f3d7e0_15234801', 'product_minimal_quantity');
?>

        <?php }?>
    </div>
<?php }
}
/* {block 'product_quantity'} */
class Block_1219215250683c1c26f35666_24451567 extends Smarty_Internal_Block
{
public $subBlocks = array (
  'product_quantity' => 
  array (
    0 => 'Block_1219215250683c1c26f35666_24451567',
  ),
);
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

                <div class="product-quantity clearfix">
                    <div class="qty">
                        <input
                            type="number"
                            name="qty"
                            id="quantity_wanted"
                            value="<?php echo htmlspecialchars((string) $_smarty_tpl->tpl_vars['product']->value['quantity_wanted'], ENT_QUOTES, 'UTF-8');?>
"
                            class="input-group"
                            min="<?php echo htmlspecialchars((string) $_smarty_tpl->tpl_vars['product']->value['minimal_quantity'], ENT_QUOTES, 'UTF-8');?>
"
                            aria-label="<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['l'][0], array( array('s'=>'Quantity','d'=>'Shop.Theme.Actions'),$_smarty_tpl ) );?>
"
                            >
                    </div>

                    <div class="add">
                        <button
                            class="btn btn-primary add-to-cart"
                            data-button-action="add-to-cart"
                            type="submit"
                            <?php if (!$_smarty_tpl->tpl_vars['product']->value['add_to_cart_url']) {?>
                                disabled
                            <?php }?>
                            >
                            <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['l'][0], array( array('s'=>'Add to cart','d'=>'Shop.Theme.Actions'),$_smarty_tpl ) );?>

                        </button>
                    </div>
                </div>
            <?php
}
}
/* {/block 'product_quantity'} */
/* {block 'product_availability'} */
class Block_1938659794683c1c26f394c2_08097154 extends Smarty_Internal_Block
{
public $subBlocks = array (
  'product_availability' => 
  array (
    0 => 'Block_1938659794683c1c26f394c2_08097154',
  ),
);
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

                <span id="product-availability">
                    <?php if ($_smarty_tpl->tpl_vars['product']->value['show_availability'] && $_smarty_tpl->tpl_vars['product']->value['availability_message']) {?>
                        <?php if ($_smarty_tpl->tpl_vars['product']->value['availability'] == 'available') {?>
                            <i class="material-icons product-available">&#xE5CA;</i>
                        <?php } elseif ($_smarty_tpl->tpl_vars['product']->value['availability'] == 'last_remaining_items') {?>
                            <i class="material-icons product-last-items">&#xE002;</i>
                        <?php } else { ?>
                            <i class="material-icons product-unavailable">&#xE14B;</i>
                        <?php }?>
                        <?php echo htmlspecialchars((string) $_smarty_tpl->tpl_vars['product']->value['availability_message'], ENT_QUOTES, 'UTF-8');?>

                    <?php }?>
                </span>
            <?php
}
}
/* {/block 'product_availability'} */
/* {block 'product_minimal_quantity'} */
class Block_1167677570683c1c26f3d7e0_15234801 extends Smarty_Internal_Block
{
public $subBlocks = array (
  'product_minimal_quantity' => 
  array (
    0 => 'Block_1167677570683c1c26f3d7e0_15234801',
  ),
);
public function callBlock(Smarty_Internal_Template $_smarty_tpl) {
?>

                <p class="product-minimal-quantity">
                    <?php if ($_smarty_tpl->tpl_vars['product']->value['minimal_quantity'] > 1) {?>
                        <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['l'][0], array( array('s'=>'The minimum purchase order quantity for the product is %quantity%.','d'=>'Shop.Theme.Checkout','sprintf'=>array('%quantity%'=>$_smarty_tpl->tpl_vars['product']->value['minimal_quantity'])),$_smarty_tpl ) );?>

                    <?php }?>
                </p>
            <?php
}
}
/* {/block 'product_minimal_quantity'} */
}
