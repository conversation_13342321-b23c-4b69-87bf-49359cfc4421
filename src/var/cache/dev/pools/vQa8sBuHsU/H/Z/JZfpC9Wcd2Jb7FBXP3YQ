<?php //PrestaShopBundle%255CEntity%255CModuleHistory%2524dateUpd

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'date_upd',
            ],
            'type' => [
                'datetime',
            ],
            'precision' => [
                null,
            ],
            'scale' => [
                null,
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
); }];
