<?php //PrestaShopBundle%255CEntity%255CModuleHistory%2524id

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\Id'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Id')),
        clone ($p['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
        clone ($p['Doctrine\\ORM\\Mapping\\GeneratedValue'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\GeneratedValue')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                1 => 'id',
            ],
            'type' => [
                1 => 'integer',
            ],
            'precision' => [
                1 => null,
            ],
            'scale' => [
                1 => null,
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
        $o[2],
    ],
    []
); }];
