(function(e){e.queryLoader2=function(t,n){var r=this;r.$el=e(t);r.el=t;r.$el.data("queryLoader2",r);r.qLimageContainer="";r.qLoverlay="";r.qLbar="";r.qLpercentage="";r.qLimages=[];r.qLbgimages=[];r.qLimageCounter=0;r.qLdone=0;r.qLdestroyed=false;r.init=function(){r.options=e.extend({},e.queryLoader2.defaultOptions,n);r.findImageInElement(r.el);if(r.options.deepSearch==true){r.$el.find("*:not(script)").each(function(){r.findImageInElement(this)})}r.createPreloadContainer();r.createOverlayLoader()};r.createPreloadContainer=function(){r.qLimageContainer=e("<div></div>").appendTo("body").css({display:"none",width:0,height:0,overflow:"hidden"});for(var t=0;r.qLbgimages.length>t;t++){e.ajax({url:r.qLbgimages[t],type:"HEAD",complete:function(e){if(!r.qLdestroyed){r.addImageForPreload(this["url"])}}})}};r.addImageForPreload=function(t){var n=e("<img />").attr("src",t);r.bindLoadEvent(n);n.appendTo(r.qLimageContainer)};r.createOverlayLoader=function(){var t="absolute";if(r.$el.prop("tagName")=="BODY"){t="fixed"}else{r.$el.css("position","relative")}r.qLoverlay=e("<div id='"+r.options.overlayId+"'></div>").css({width:"100%",height:"100%",backgroundColor:r.options.backgroundColor,backgroundPosition:"fixed",position:t,zIndex:666999,top:0,left:0}).appendTo(r.$el);r.qLbar=e("<div id='qLbar'></div>").css({height:"35px",marginTop:"0px",width:"0%",position:"absolute"}).appendTo(r.qLoverlay);if(r.options.percentage==true){r.qLpercentage=e("<div id='qLpercentage'></div>").text("0%").css({position:"absolute",bottom:"10px",right:"10px",textAlign:"center"}).appendTo(r.qLoverlay)}if(!r.qLimages.length){r.destroyContainers()}};r.destroyContainers=function(){r.qLdestroyed=true;r.qLimageContainer.remove();r.qLoverlay.remove()};r.findImageInElement=function(t){var n="";var i=e(t);var s="normal";if(i.css("background-image")!="none"){n=i.css("background-image");s="background"}else if(typeof i.attr("src")!="undefined"&&t.nodeName.toLowerCase()=="img"){n=i.attr("src")}if(n.indexOf("gradient")==-1){n=n.replace(/url\(\"/g,"");n=n.replace(/url\(/g,"");n=n.replace(/\"\)/g,"");n=n.replace(/\)/g,"");var o=n.split(", ");for(var u=0;u<o.length;u++){if(o[u].length>0&&r.qLimages.indexOf(o[u])==-1&&!o[u].match(/^(data:)/i)){var a="";if(r.isIE()||r.isOpera()){a="?rand="+Math.random();r.qLbgimages.push(o[u]+a)}else{if(s=="background"){r.qLbgimages.push(o[u])}else{r.bindLoadEvent(i)}}r.qLimages.push(o[u])}}}};r.isIE=function(){return navigator.userAgent.match(/msie/i)};r.isOpera=function(){return navigator.userAgent.match(/Opera/i)};r.bindLoadEvent=function(e){r.qLimageCounter++;imagesLoaded(e,function(){r.completeImageLoading(this)})};r.completeImageLoading=function(e){r.qLdone++;var t=r.qLdone/r.qLimageCounter*100;r.qLbar.stop().animate({width:t+"%",minWidth:t+"%"},200);if(r.options.percentage==true){r.qLpercentage.text(Math.ceil(t)+"%")}if(r.qLdone==r.qLimageCounter){r.endLoader()}};r.endLoader=function(){r.qLdestroyed=true;r.onLoadComplete()};r.onLoadComplete=function(){if(r.options.completeAnimation=="grow"){var t=500;r.qLbar.stop().animate({width:"100%"},t,function(){e(this).animate({top:"0%",width:"100%",height:"100%"},500,function(){e("#"+r.options.overlayId).fadeOut(500,function(){e(this).remove();r.destroyContainers();r.options.onComplete()})})})}else{e("#"+r.options.overlayId).fadeOut(500,function(){e("#"+r.options.overlayId).remove();r.destroyContainers();r.options.onComplete()})}};r.init()};e.queryLoader2.defaultOptions={onComplete:function(){},backgroundColor:"#000",barColor:"#fff",overlayId:"qLoverlay",barHeight:1,percentage:false,deepSearch:true,completeAnimation:"fade",minimumTime:500};e.fn.queryLoader2=function(t){return this.each(function(){new e.queryLoader2(this,t)})}})(jQuery);if(!Array.prototype.indexOf){Array.prototype.indexOf=function(e){var t=this.length>>>0;var n=Number(arguments[1])||0;n=n<0?Math.ceil(n):Math.floor(n);if(n<0)n+=t;for(;n<t;n++){if(n in this&&this[n]===e)return n}return-1}}
