// ui-datepicker: The outer container of the datepicker. If the datepicker is inline, this element will additionally have a ui-datepicker-inline class. If the isRTL option is set, this element will additionally have a class of ui-datepicker-rtl.
// ui-datepicker-header: The container for the datepicker's header.
// ui-datepicker-prev: The control used to select previous months.
// ui-datepicker-next: The control used to select subsequent months.
// ui-datepicker-title: The container for the datepicker's title containing the month and year.
// ui-datepicker-month: The textual display of the month or a <select> element if the changeMonth option is set.
// ui-datepicker-year: The textual display of the year or a <select> element if the changeYear option is set.
// ui-datepicker-calendar: The table that contains the calendar itself.
// ui-datepicker-week-end: Cells containing weekend days.
// ui-datepicker-other-month: Cells containing days that occur in a month other than the currently selected month.
// ui-datepicker-unselectable: Cells containing days that are not selectable by the user.
// ui-datepicker-current-day: The cell containing the selected day.
// ui-datepicker-today: The cell containing today's date.
// ui-datepicker-buttonpane: The buttonpane that is used when the showButtonPanel option is set.
// ui-datepicker-current: The button used to select today's date.

.ui-datepicker {
  top: 0;
  left: 0;
  padding: 4px;
  margin-top: 1px;
  @include border-radius(4px);

  &::before {
    position: absolute;
    top: -7px;
    left: 6px;
    display: inline-block !important;
    content: "";
    border-right: 7px solid transparent;
    border-bottom: 7px solid #ccc;
    border-bottom-color: rgba(#000, 0.2);
    border-left: 7px solid transparent;
  }

  &::after {
    position: absolute;
    top: -6px;
    left: 7px;
    display: inline-block !important;
    content: "";
    border-right: 6px solid transparent;
    border-bottom: 6px solid #fff;
    border-left: 6px solid transparent;
  }

  .ui-datepicker-header {
    font-weight: 500;
    color: #808080;
    background: transparent !important;
  }

  .ui-state-default {
    background: transparent !important;
  }

  .ui-state-highlight {
    background: #fefbe2 !important;
    border: solid 1px darken(#fefbe2, 50%) !important;
  }

  .ui-state-active {
    color: #fff !important;
    background: $primary-color !important;
    border: solid 1px darken($primary-color, 10%) !important;
  }
}
