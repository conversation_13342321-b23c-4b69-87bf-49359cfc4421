.media-product-pack {
  position: relative;
  width: 200px;
  padding: 4px;
  margin-bottom: 10px;
  overflow: hidden;
  border: solid 2px #eee;
  @include margin-right(10px);
  @include border-radius(4px);
  @include float(left);

  .media-product-pack-img {
    width: 100%;
    padding-bottom: 5px;
    margin-bottom: 5px;
    border-bottom: 1px solid #ccc;
  }

  .media-product-pack-title {
    display: inline-block;
    width: 100%;
    padding: 0 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .media-product-pack-ref {
    display: inline-block;
    width: 100%;
    padding: 0 5px;
    overflow: hidden;
    color: #aaa;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .media-product-pack-action {
    position: absolute;
    top: 7px;
    text-align: center;
    border: solid 1px #ccc;
    @include right(7px);
    @include border-radius(4px);
  }

  .media-product-pack-quantity {
    position: absolute;
    top: 7px;
    min-width: 35px;
    height: 25px;
    padding: 0 4px;
    line-height: 23px;
    text-align: center;
    background-color: #fff;
    border: solid 1px #ccc;
    @include left(7px);
    @include border-radius(4px);
  }
}
