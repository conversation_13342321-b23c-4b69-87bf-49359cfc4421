$color-bright: #fff;

$color-calendar: #00a4e7;
$color-calendar-compare: #ff8000;

$color-calendar-day: $color-bright;

$color-calendar-outrange: #ccc;

$color-calendar-day-selected: $color-calendar;
$color-calendar-day-selected-compare: $color-calendar-compare;

$color-calendar-range: lighten($color-calendar, 10%);
$color-calendar-range-compare: lighten($color-calendar-compare, 10%);

#datepicker {
  .input-selected {
    margin: 0;
  }

  #date-start.input-selected,
  #date-end.input-selected {
    border: solid 1px $color-calendar-day-selected;
  }

  #date-start-compare.input-selected,
  #date-end-compare.input-selected {
    border: solid 1px $color-calendar-day-selected-compare;
  }

  div#datepicker-form {
    #date-range {
      border: solid 1px darken($color-calendar, 5%);

      .form-date-heading {
        background-color: darken($color-calendar, 5%);
      }
    }

    #date-compare {
      border: solid 1px darken($color-calendar-compare, 5%);

      .form-date-heading {
        background-color: darken($color-calendar-compare, 5%);
      }
    }

    .form-date-heading {
      height: 30px;
      line-height: 30px;
      @include padding(0, 0, 0, 8px);

      .title,
      .checkbox-title label {
        margin-top: 0;
        font-size: 1.15em;
        font-weight: 200;
        line-height: 2em;
        color: $color-bright;
        text-transform: uppercase;
      }

      .btn-default {
        font-weight: 700;
        color: $color-bright;
        background-color: rgba($color-bright, 0.3);
        border: none;
      }

      select {
        @include margin(2px,2px,0,0);
        @include padding(2px, 4px, 2px, 4px);
        height: 1.625rem;
        line-height: 1;
      }
    }

    .form-date-body,
    .form-date-actions {
      display: inline-block;
      width: 100%;
      padding: 10px;
      background-color: #fff;
    }

    .form-date-actions {
      background-color: #fcfdfe;
      border: solid 1px #ccc;

      .btn.pull-right {
        @extend .btn-primary;
      }
    }

    .form-date-group {
      width: 100%;
      margin: 0 auto 6px;
      clear: both;

      label {
        margin-top: 5px;
        font-size: 0.8em;
        font-weight: 700;
        color: #666;
        text-transform: uppercase;
      }

      input {
        @include margin(0, 2px, 0, 0);
      }

      input[type="text"] {
        @include margin(0, 8px, 0, 0);
        width: 35%;
        height: 2.25rem;
      }

      button {
        @include margin(5px, 0, 0, 0);
      }
    }
  }

  .daterangepicker {
    position: relative;
    width: 100%;
    font-size: 13px;
    border: solid 1px #ccc;
    @include margin(0, 0, 8px, 0);

    table {
      width: 100%;
      margin: 0;
      border-spacing: 1px;
    }

    thead {
      tr th {
        height: 1.5em;
        font-size: 0.7em;
        color: #fff;
        text-transform: uppercase;
        background-color: darken($color-calendar, 5%);
      }

      tr:first-child th {
        font-size: 1.4em;
        font-weight: 200;
        color: $color-calendar-day-selected;
        cursor: pointer;
        background-color: $color-bright;
        border-bottom: 1px solid darken($color-calendar, 5%);
      }
    }

    td,
    th {
      position: relative;
      padding: 0;
      text-align: center;
    }

    tr {
      border-bottom: solid 1px #fff;
    }

    td {
      height: 2.4em;
      padding: 0 8px;
      color: $color-calendar-day-selected;
      @include transition-property(all);
      @include transition-duration(0.2s);
      @include transition-timing-function(ease-out);

      &.day::after {
        position: absolute;
        top: 0;
        z-index: 0;
        display: block;
        width: 100%;
        content: "";
        background-color: transparent;
        @include left(0);
      }

      &.day:hover {
        cursor: pointer;
      }

      &.day.disabled {
        color: $color-calendar-outrange;
      }

      &.old,
      &.new {
        color: $color-calendar-outrange;
      }

      &.start-selected:not(.old):not(.new):not(.end-selected) {
        font-weight: 700;
        color: $color-bright;
        background-color: $color-calendar-day-selected;
        @include border-left-radius(15px);

        &.range-compare {
          background-color: mix($color-calendar-day-selected, $color-calendar-range-compare, 70%);
          @include border-left-radius(0);
        }

        &.end-selected-compare,
        &.start-selected-compare {
          background-color: mix($color-calendar-day-selected, $color-calendar-day-selected-compare, 70%);
        }

        &.end-selected-compare {
          @include border-radius(0);
        }
      }

      &.end-selected:not(.old):not(.new):not(.start-selected) {
        font-weight: 700;
        color: $color-bright;
        background-color: $color-calendar-day-selected;
        @include border-right-radius(15px);

        &.range-compare {
          background-color: mix($color-calendar-day-selected, $color-calendar-range-compare, 70%);
          @include border-right-radius(0);
        }

        &.end-selected-compare,
        &.start-selected-compare {
          background-color: mix($color-calendar-day-selected, $color-calendar-day-selected-compare, 70%);
        }

        &.start-selected-compare {
          @include border-radius(0);
        }
      }

      &.start-selected-compare:not(.old):not(.new):not(.end-selected-compare) {
        font-weight: 700;
        color: $color-bright;
        background-color: $color-calendar-day-selected-compare;
        @include border-left-radius(15px);

        &.range {
          background-color: mix($color-calendar-day-selected-compare, $color-calendar-range, 70%);
          @include border-left-radius(0);
        }
      }

      &.end-selected-compare:not(.old):not(.new):not(.start-selected-compare) {
        font-weight: 700;
        color: $color-bright;
        background-color: $color-calendar-day-selected-compare;
        @include border-right-radius(15px);

        &.range {
          background-color: mix($color-calendar-day-selected-compare, $color-calendar-range, 70%);
          @include border-right-radius(0);
        }
      }

      &.end-selected.start-selected {
        font-weight: 700;
        color: $color-bright;
        background-color: $color-calendar-day-selected;
        @include border-radius(15px);

        &.range-compare {
          background-color: mix($color-calendar-day-selected, $color-calendar-range-compare, 70%);
          @include border-radius(0);
        }
      }

      &.end-selected-compare.start-selected-compare {
        font-weight: 700;
        color: $color-bright;
        background-color: $color-calendar-day-selected-compare;
        @include border-radius(15px);

        &.range {
          background-color: mix($color-calendar-day-selected-compare, $color-calendar-range, 70%);
          @include border-radius(0);
        }
      }

      &.range {
        color: lighten($color-calendar-range, 35%);
        background-color: $color-calendar-range;

        &.range-compare {
          background-color: mix($color-calendar-range, $color-calendar-range-compare, 50%);
        }
      }

      &.range-compare {
        color: lighten($color-calendar-range-compare, 35%);
        background-color: $color-calendar-range-compare;

        &.range {
          background-color: mix($color-calendar-range, $color-calendar-range-compare, 50%);
        }
      }

      &.today {
        background-color: $color-bright;
      }

      span {
        display: block;
        float: left;
        width: 33.333%;
        height: 3em;
        padding: 0;
        font-weight: 400;
        line-height: 3em;
        text-transform: uppercase;
        cursor: pointer;

        &:hover {
          color: #fff;
          background-color: $color-calendar-day-selected;
        }

        &.active {
          color: #fff;
          background-color: $color-calendar-day-selected;
        }

        &.old {
          color: $color-calendar-outrange;
        }
      }
    }

    th {
      &.next {
        @include text-align(right);
        @include padding(0, 10px, 0, 0);
      }

      &.prev {
        @include text-align(left);
        @include padding(0, 0, 0, 10px);
      }

      &.next,
      &.prev,
      &.month-switch {
        height: 2em;
        font-size: 1.3em;
        line-height: 2em;

        &:hover {
          color: #fff;
          background-color: darken($color-calendar, 5%);
        }
      }
    }
  }
}

#datepickerExpand {
  i {
    vertical-align: middle;
  }
}

//chevrons are disabled time to figure out the firefox incompatibility..
.chevron-left,
.chevron-right {
  position: relative;
}

.chevron-left::before,
.chevron-right::before {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  content: "";
  border-style: solid;
}

.chevron-left::before {
  top: 0;
  background-color: $color-calendar-range;
  border-color: transparent transparent transparent $color-calendar-day-selected;
  @include right(0);
  @include border-width(1.2em, 0, 1.2em, 0.5em);
}

.chevron-right::before {
  top: 0;
  background-color: $color-calendar-day-selected;
  border-color: transparent transparent transparent $color-calendar-range;
  @include left(0);
  @include border-width(1.2em, 0, 1.2em, 0.5em);
}

.input-complete {
  -webkit-animation: one 0.2s ease-in-out;
  -moz-animation: one 0.2s ease-in-out;
  -ms-animation: one 0.2s ease-in-out;
  -o-animation: one 0.2s ease-in-out;
  animation: one 0.2s ease-in-out;
}
// stylelint-disable-next-line
@-webkit-keyframes one {
  0% {
    background-color: $color-calendar;
  }

  100% {
    background-color: $color-bright;
  }
}

// stylelint-disable-next-line
@-webkit-keyframes two {
  0% {
    @include box-shadow($color-calendar 0 0 0 20px inset);
  }

  100% {
    @include box-shadow($color-calendar 0 0 0 inset);
  }
}
