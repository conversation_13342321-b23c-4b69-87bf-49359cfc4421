.page-head {
  position: fixed;
  top: $size-header-height;
  left: 0;
  z-index: 499;
  width: 100%;
  padding: 0 0 0 210px;
  margin: 0;
  color: #363a41;
  background-color: #fff;
  border-bottom: #dfdfdf 1px solid;

  transition: padding-left 0.5s ease-out;

  .mobile & {
    top: 2.5rem;
    padding-top: 1rem;
  }

  @media (max-width: breakpoint-max("md")) {
    padding: 0;
  }

  &.with-tabs {
    height: 140px;

    @include media-breakpoint-down(md) {
      height: auto;
    }
  }

  > .wrapper {
    padding-left: 15px;
  }

  .page-title {
    position: relative;
    float: left;
    padding: 0.3125rem 0;
    margin: 0 0 0.9375rem;
    clear: left;
    font: 20pt $font-family-sans-serif;
    line-height: 1.2;
    color: #363a41;
    white-space: nowrap;

    @at-root .adminmodules & {
      margin: 0 0 1.25rem;
    }

    a {
      border-bottom: dotted 1px #fff;

      &:hover {
        color: #000;
        border-color: #000;
      }
    }
  }

  a {
    color: #fff;
    text-decoration: none;
  }

  h4.page-subtitle {
    @include float(left);
    position: absolute;
    margin-top: 60px;
    font-family: $url-font-content-name;
    @include left($widthSidebarNav + 17px);
    @media (max-width: $screen-tablet) {
      @include left(70px);
    }

    @at-root .adminmodules & {
      margin-top: 67px;
    }
  }

  .toolbarBox {
    background-color: transparent;
    @include right(0);

    @media (max-width: $screen-tablet) {
      width: 100%;
    }

    #toolbar-nav {
      border: none;

      @media (max-width: $screen-tablet) {
        padding-top: 25px;
        padding-bottom: 10px;
        margin-top: 30px;
        background: #fff;
      }
    }

    li {
      &:last-of-type {
        .toolbar_btn {
          margin-right: 10px;
        }
      }
    }

    .btn-toolbar {
      justify-content: flex-end;
      margin: 0;
      @include padding(3px, 0, 0, 0);

      .toolbar_btn {
        position: relative;
        padding: 8px 16px;
        overflow: hidden;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.5;
        color: $main-color;
        color: $medium-gray;
        text-align: center;
        text-overflow: ellipsis;
        text-shadow: none;
        white-space: nowrap;
        border: 1px solid $medium-gray;
        border-radius: 4px;
        transition: 0.25s ease-out;
        @include margin(0, 14px, 2px, 0);

        &:hover {
          color: #fff;
          background: $btn-secondary-hover;
          border-color: $btn-secondary-hover;
        }

        &:focus,
        &:active {
          color: #fff;
          background: $btn-secondary-active-color;
          border-color: $btn-secondary-active-color;
          box-shadow: 0 0 0 0.2rem rgba(108, 134, 142, 0.5);
        }

        @media (max-width: $screen-desktop) {
          font-size: 11px;
        }

        i {
          display: none;
          color: $toolbar-buttons-color;

          &:hover {
            color: $toolbar-buttons-hover-color;
          }
        }

        &[id^="page-"] {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          background: $primary-color;
          border-color: $primary-color;

          i {
            display: block;
            width: 20px;
            height: auto;
            margin-right: 4px;
            font-size: 16px;
            color: #fff;
          }

          &:hover {
            color: #fff;
            background: $primary-light-color;
            border-color: $primary-light-color;
          }

          &:focus,
          &:active {
            color: #fff;
            background: $btn-primary-active-bg;
            border-color: $btn-primary-active-bg;
            box-shadow: 0 0 0 0.2rem rgba(70, 196, 221, 0.5);
          }
        }
      }

      .dropdown-toolbar {
        display: none;
        @include float(right);
        @include border-radius(3px);
        @media (max-width: $screen-sm) {
          display: block;
        }
      }
    }
  }

  ul.page-breadcrumb {
    float: left;
    padding: 0.3125rem 0;
    margin: 0;
    font-size: 12px;
    line-height: 1.5;
    color: #000;
    color: rgba(#000, 0.5);
    white-space: nowrap;
    list-style: none;
    /* stylelint-disable */
    background: transparent !important;
    border: none !important;
    /* stylelint-enable */
    @include border-radius(0);

    li {
      i {
        font-size: 10px;
      }

      &.breadcrumb-container i,
      &.breadcrumb-current i {
        position: absolute;
        top: 2px;
        font-size: 42px;
        color: darken($secondary-color, 15%);
        @include padding-right(5px);
        @include border-right(solid 1px darken($secondary-color,15%));
        @include left(-60px);
      }

      &::before {
        color: darken($secondary-color, 15%);
      }

      a {
        color: #363a41;

        &:hover {
          color: #363a41;
        }
      }
    }
  }

  .page-head-tabs {
    clear: left;
    background-color: #fff;

    ul {
      font-weight: 400;
      border-top: 1px solid #dfdfdf;
      border-bottom: 1px solid #dfdfdf;
    }

    li {
      display: inline-block;

      a {
        box-sizing: border-box;
        padding: 0.9375rem 1.25rem;
        font-size: 0.875rem;
        line-height: 1.5;
        color: #6c868e;
        vertical-align: middle;

        &.current {
          color: #363a41;
          background-color: #f4f9fb;
          border-bottom: 3px solid #3ed2f0;
        }
      }
    }
  }
}

.page-sidebar-closed {
  .page-head {
    padding-left: 4.0625rem;
  }
}

.page-header-toolbar-back {
  display: inline-block;
  border: none;
}
