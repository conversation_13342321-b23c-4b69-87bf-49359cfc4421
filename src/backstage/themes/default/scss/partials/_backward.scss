.page-sidebar:not(.page-sidebar-closed) .nobootstrap {
  margin-left: 200px;
}

.nobootstrap {
  min-width: 1200px;
  padding: 110px 30px 100px;
  background-color: #fff;

  .form-group {
    float: left;
    width: 100%;
    margin-bottom: 1em;
    clear: both;

    > div {
      float: left;
    }
  }

  .panel:not(.bootstrap) {
    box-sizing: border-box;
    float: left;
    width: 100%;
    padding: 1em;
    margin: 0 0 10px;
    color: #585a69;
    background-color: #ebedf4;
    border: 1px solid #ccced7;

    img {
      padding: 0 4px 0 0;
      vertical-align: bottom;
    }

    h3 {
      position: relative;
      top: -25px;
      display: inline-block;
      padding: 0.2em 0.5em;
      margin: 0;
      font-weight: 700;
      text-align: left;
      background: #ebedf4;
      border: 1px solid #ccced7;

      a {
        color: #585984;
        text-decoration: none;
      }

      i {
        font-family: FontAwesome, sans-serif;
      }
    }

    .help-block {
      float: left;
      clear: both;
    }

    .switch {
      float: left;
      clear: both;
      line-height: 1.3em;

      label {
        float: left;
        width: auto;
        padding: 0 10px 0 5px;
      }

      input[type="checkbox"],
      input[type="radio"] {
        float: left;
      }
    }

    .radio {
      label {
        width: auto;
        margin-right: 4px;
        clear: both;
      }
    }

    button {
      padding: 3px 8px;
      margin: 5px 0;
      color: #000;
      text-align: center;
      text-decoration: none;
      text-shadow: 0 1px 0 #fff;
      white-space: nowrap;
      vertical-align: middle;
      cursor: pointer;
      // stylelint-disable-next-line
      background: -moz-linear-gradient(center top, #f9f9f9, #e3e3e3) repeat scroll 0 0 transparent;
      background: -webkit-gradient(linear, center top, center bottom, from(#f9f9f9), to(#e3e3e3)) repeat scroll 0 0 transparent;
      border-color: #ccc #bbb #a0a0a0;
      border-style: solid;
      border-width: 1px;
      border-right: 1px solid #bbb;
      border-left: 1px solid #bbb;
      border-radius: 3px;
      outline: medium none;

      &:hover {
        border-color: #aaa #999 #888;
      }
    }

    .tree-panel-heading-controls {
      padding: 5px;
      margin: -8px -8px 20px;
      font-size: 13px;
      background-color: #dde0e9;
      border-bottom: solid 1px #dfdfdf;

      a {
        display: inline-block;
        padding: 0 8px;
        border-right: solid 1px #888;
      }
    }

    .tree {
      padding: 0 0 0 20px;
      list-style: none;

      input {
        margin-right: 4px;
        line-height: normal;
        vertical-align: baseline;
      }

      i {
        font-size: $icon-size-base;
      }

      .tree-item-name,
      .tree-folder-name {
        padding: 2px 5px;
        @include border-radius(4px);

        label {
          float: none;
          font-size: 13px;
          font-weight: 400;
          text-shadow: none;
        }

        &:hover {
          cursor: pointer;
          background-color: #eee;
        }
      }

      .tree-selected {
        background-color: #ccced7;

        &:hover {
          background-color: darken(#ccced7, 15%);
        }

        i.tree-dot {
          background-color: #eee;
        }
      }

      .tree-folder,
      .tree-item {
        margin: 3px 0;
      }

      i.tree-dot {
        position: relative;
        display: inline-block;
        width: 6px;
        height: 6px;
        margin: 0 4px;
        background-color: #ccc;
        @include border-radius(6px);
      }

      .tree-item-disable,
      .tree-folder-name-disable {
        color: #ccc;

        &:hover {
          color: #ccc;
          background-color: none;
        }
      }
    }
  }

  form p {
    padding: 0 0 0.5em;
    margin: 0.5em 0 0;
  }

  fieldset {
    padding: 1em;
    margin: 0 0 10px;
    color: #585a69;
    background-color: #ebedf4;
    border: 1px solid #ccced7;

    img {
      padding: 0 4px 0 0;
      vertical-align: bottom;
    }
  }

  legend {
    padding: 0.2em 0.5em;
    margin: 0;
    font-weight: 700;
    text-align: left;
    background: #ebedf4;
    border: 1px solid #ccced7;

    a {
      color: #585984;
      text-decoration: none;
    }
  }

  label {
    float: left;
    width: 250px;
    padding: 0.2em 0.5em 0 0;
    font-weight: 700;
    color: #585a69;
    text-align: right;
    text-shadow: 0 1px 0 #fff;

    &.t {
      float: none;
      padding: 0 5px;
      margin: 0;
      clear: none;
      font-size: 12px;
      font-weight: 500;
      text-shadow: none;
    }
  }

  a {
    color: #415260;
  }

  .pull-right,
  .pull-left {
    float: none;
  }

  .clear {
    clear: both;
  }

  .margin-form {
    padding: 0 0 1em 260px;
    font-size: 0.85em;
    color: #7f7f7f;
  }

  .button {
    padding: 3px 8px;
    margin: 5px 0;
    color: #000;
    text-align: center;
    text-decoration: none;
    text-shadow: 0 1px 0 #fff;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    // stylelint-disable-next-line
    background: -moz-linear-gradient(center top, #f9f9f9, #e3e3e3) repeat scroll 0 0 transparent;
    background: -webkit-gradient(linear, center top, center bottom, from(#f9f9f9), to(#e3e3e3)) repeat scroll 0 0 transparent;
    border-color: #ccc #bbb #a0a0a0;
    border-style: solid;
    border-width: 1px;
    border-right: 1px solid #bbb;
    border-left: 1px solid #bbb;
    border-radius: 3px;
    outline: medium none;

    &:hover {
      border-color: #aaa #999 #888;
    }
  }

  input {
    vertical-align: middle;
  }

  select {
    font-size: 12px;
    border: 1px solid #ccc;
  }

  input,
  textarea,
  option {
    padding: 0;
    margin: 0;
    font-size: 12px;
    color: #000;
  }

  input[type="text"],
  input[type="password"],
  input[type="file"],
  textarea {
    padding: 2px 4px;
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
  }

  .table_grid {
    width: 100%;
  }

  .table {
    padding: 0;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 3px;

    th a {
      text-decoration: underline;

      &:hover {
        text-decoration: none;
      }
    }

    tr {
      th {
        padding: 4px 6px;
        font-size: 13px;
        color: #333;
        text-align: left;
        background-color: #f1f1f1;

        &.right {
          text-align: center;
        }
      }

      td {
        padding: 4px 4px 4px 6px;
        font-size: 12px;
        color: #333;
        border-bottom: 1px solid #ccc;
      }

      &.row_hover.filter:hover td {
        background: #f1f9ff;
      }

      td.row_hover:hover table tr td {
        background: none;
      }

      &.action_details td {
        background: #fafafa;
      }

      &.alt_row.action_details td {
        background: #e8e8e8;
      }

      td.empty {
        background: #fff !important;
        border-bottom: none;
      }

      td.first {
        border-left: 1px solid #dedede;
      }

      td.last {
        border-right: 1px solid #dedede;
      }

      &.small td {
        height: 15px;
      }

      &.last td {
        border-bottom: none;
      }

      .filter {
        background-color: #f1f9ff;
      }
      // Helper 1.6
    }
  }
}
