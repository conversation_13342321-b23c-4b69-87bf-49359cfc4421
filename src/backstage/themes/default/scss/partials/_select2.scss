/*
Version: 3.4.6 Timestamp: Sat Mar 22 22:30:15 EDT 2014
*/
.select2-container {
  position: relative;
  display: inline-block;
  *display: inline;
  margin: 0;
  vertical-align: middle;

  /* inline-block for ie7 */
  zoom: 1;
}

.select2-container,
.select2-drop,
.select2-search,
.select2-search input {
  /*
    Force border-box so that % widths fit the parent
    container without overlap because of margin/padding.
    More Info : http://www.quirksmode.org/css/box.html
  */
  -webkit-box-sizing: border-box; /* webkit */
  -moz-box-sizing: border-box; /* firefox */
  box-sizing: border-box; /* css3 */
}

.select2-container .select2-choice {
  position: relative;
  display: block;
  height: 26px;
  padding: 0 0 0 8px;
  overflow: hidden;
  line-height: 26px;
  color: #444;
  text-decoration: none;
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  background-color: #fff;
  // stylelint-disable-next-line
  background-image: linear-gradient(to top, #eee 0%, #fff 50%);

  background-clip: padding-box;

  border: 1px solid #aaa;

  border-radius: 4px;

  -webkit-touch-callout: none;
}

.select2-container.select2-drop-above .select2-choice {
  border-bottom-color: #aaa;
  border-radius: 0 0 4px 4px;
}

.select2-container.select2-allowclear .select2-choice .select2-chosen {
  margin-right: 42px;
}

.select2-container .select2-choice > .select2-chosen {
  display: block;
  float: none;
  width: auto;
  margin-right: 26px;
  overflow: hidden;

  text-overflow: ellipsis;

  white-space: nowrap;
}

.select2-container .select2-choice abbr {
  position: absolute;
  top: 8px;
  right: 24px;
  display: none;
  width: 12px;
  height: 12px;

  font-size: 1px;
  text-decoration: none;
  cursor: pointer;
  background: url("../img/select2.png") right top no-repeat;

  border: 0;
  outline: 0;
}

.select2-container.select2-allowclear .select2-choice abbr {
  display: inline-block;
}

.select2-container .select2-choice abbr:hover {
  cursor: pointer;
  background-position: right -11px;
}

.select2-drop-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9998;
  width: auto;
  min-width: 100%;
  height: auto;
  min-height: 100%;
  padding: 0;
  margin: 0;

  /* styles required for IE to work */
  background-color: #fff;
  filter: alpha(opacity=0);
  border: 0;
  opacity: 0;
}

.select2-drop {
  position: absolute;
  top: 100%;
  z-index: 9999;
  width: 100%;
  margin-top: -1px;
  color: #000;

  background: #fff;
  border: 1px solid #ccc;
  border-top: 0;

  border-radius: 0 0 4px 4px;

  -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
}

.select2-drop.select2-drop-above {
  margin-top: 1px;
  border-top: 1px solid #aaa;
  border-bottom: 0;

  border-radius: 4px 4px 0 0;

  -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 -4px 5px rgba(0, 0, 0, 0.15);
}

.select2-drop-active {
  border-top: none;
}

.select2-drop-auto-width {
  width: auto;
  border-top: 1px solid #aaa;
}

.select2-drop-auto-width .select2-search {
  padding-top: 4px;
}

.select2-container .select2-choice .select2-arrow {
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
  width: 18px;
  height: 100%;
  background-color: #eee;
  background-clip: padding-box;
  border-left: 1px solid #ccc;
  border-radius: 0 4px 4px 0;
}

.select2-container .select2-choice .select2-arrow b {
  display: block;
  width: 100%;
  height: 100%;
  background: url("../img/select2.png") no-repeat 0 1px;
}

.select2-search {
  position: relative;
  z-index: 10000;
  display: inline-block;
  width: 100%;
  min-height: 26px;
  padding-top: 4px;
  padding-right: 4px;
  padding-left: 4px;
  margin: 0;
  white-space: nowrap;
}

.select2-search input {
  width: 100%;
  height: auto !important;
  min-height: 26px;
  padding: 4px 20px 4px 5px;
  margin: 0;
  font-family: sans-serif;
  font-size: 1em;

  background: #fff url("../img/select2.png") no-repeat 100% -22px;
  background: url("../img/select2.png") no-repeat 100% -22px;

  border: 1px solid #aaa;
  border-radius: 0;

  outline: 0;

  -webkit-box-shadow: none;
  box-shadow: none;
}

.select2-drop.select2-drop-above .select2-search input {
  margin-top: 4px;
}

.select2-search input.select2-active {
  background: #fff url("../img/select2-spinner.gif") no-repeat 100%;
  background: url("../img/select2-spinner.gif") no-repeat 100%;
}

.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
  outline: none;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.select2-dropdown-open .select2-choice {

  background-color: #eee;
  border-bottom-color: transparent;
  border-bottom-right-radius: 0;

  border-bottom-left-radius: 0;
  -webkit-box-shadow: 0 1px 0 #fff inset;
  box-shadow: 0 1px 0 #fff inset;
}

.select2-dropdown-open.select2-drop-above .select2-choice,
.select2-dropdown-open.select2-drop-above .select2-choices {
  border-top-color: transparent;
}

.select2-dropdown-open .select2-choice .select2-arrow {
  background: transparent;
  filter: none;
  border-left: none;
}

.select2-dropdown-open .select2-choice .select2-arrow b {
  background-position: -18px 1px;
}

.select2-hidden-accessible {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  border: 0;
}

/* results */
.select2-results {
  position: relative;
  max-height: 200px;
  padding: 0 0 0 4px !important;
  margin: 4px 4px 4px 0 !important;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.select2-results ul.select2-result-sub {
  padding-left: 0;
  margin: 0;
}

.select2-results ul.select2-result-sub > li .select2-result-label { padding-left: 20px; }

.select2-results ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 40px; }

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 60px; }

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 80px; }

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 100px; }

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 110px; }

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 120px; }

.select2-results li {
  display: list-item;
  list-style: none;
  background-image: none;
}

.select2-results li.select2-result-with-children > .select2-result-label {
  font-weight: 700;
}

.select2-results .select2-result-label {

  min-height: 1em;
  padding: 3px 7px 4px;
  margin: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  -webkit-touch-callout: none;
}

.select2-results .select2-highlighted {
  color: #fff;
  background: #3875d7;
}

.select2-results li em {
  font-style: normal;
  background: #feffde;
}

.select2-results .select2-highlighted em {
  background: transparent;
}

.select2-results .select2-highlighted ul {
  color: #000;
  background: #fff;
}


.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
  display: list-item;
  padding: 5px;
  background: #f4f4f4;
}

/*
disabled look for disabled choices in the results dropdown
*/
.select2-results .select2-disabled.select2-highlighted {
  display: list-item;
  color: #666;
  cursor: default;
  background: #f4f4f4;
}

.select2-results .select2-disabled {
  display: list-item;
  cursor: default;
  background: #f4f4f4;
}

.select2-results .select2-selected {
  display: none;
}

.select2-more-results.select2-active {
  background: #f4f4f4 url("../img/select2-spinner.gif") no-repeat 100%;
}

.select2-more-results {
  display: list-item;
  background: #f4f4f4;
}

/* disabled styles */

.select2-container.select2-container-disabled .select2-choice {
  cursor: default;
  background-color: #f4f4f4;
  background-image: none;
  border: 1px solid #ddd;
}

.select2-container.select2-container-disabled .select2-choice .select2-arrow {
  background-color: #f4f4f4;
  background-image: none;
  border-left: 0;
}

.select2-container.select2-container-disabled .select2-choice abbr {
  display: none;
}


/* multiselect */

.select2-container-multi .select2-choices {
  position: relative;
  height: auto !important;
  height: 1%;
  min-height: 26px;
  padding: 0;
  margin: 0;
  overflow: hidden;
  cursor: text;
  background-color: #fff;
  border: 1px solid #aaa;
}

.select2-locked {
  padding: 3px 5px !important;
}

.select2-container-multi.select2-container-active .select2-choices {
  outline: none;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.select2-container-multi .select2-choices li {
  float: left;
  list-style: none;
}

html[dir="rtl"] .select2-container-multi .select2-choices li
{
  float: right;
}

.select2-container-multi .select2-choices .select2-search-field {
  padding: 0;
  margin: 0;
  white-space: nowrap;
}

.select2-container-multi .select2-choices .select2-search-field input {
  padding: 5px;
  margin: 1px 0;

  font-family: sans-serif;
  font-size: 100%;
  color: #666;
  background: transparent !important;
  border: 0;
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.select2-container-multi .select2-choices .select2-search-field input.select2-active {
  background: #fff url("../img/select2-spinner.gif") no-repeat 100% !important;
}

.select2-default {
  color: #999 !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
  position: relative;
  padding: 3px 5px 3px 18px;
  margin: 3px 0 3px 5px;

  line-height: 13px;
  color: #333;
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  background-color: #e4e4e4;
  // stylelint-disable
  background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eee));
  background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
  background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
  background-image: linear-gradient(to top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
  // stylelint-enable
  filter: progid:dximagetransform.microsoft.gradient(startColorstr="#eeeeee", endColorstr="#f4f4f4", GradientType=0);

  background-clip: padding-box;
  border: 1px solid #aaa;

  border-radius: 3px;

  -webkit-touch-callout: none;
}

html[dir="rtl"] .select2-container-multi .select2-choices .select2-search-choice
{
  margin-right: 5px;
  margin-left: 0;
}

.select2-container-multi .select2-choices .select2-search-choice .select2-chosen {
  cursor: default;
}

.select2-container-multi .select2-choices .select2-search-choice-focus {
  background: #d4d4d4;
}

.select2-search-choice-close {
  position: absolute;
  top: 4px;
  right: 3px;
  display: block;
  width: 12px;
  height: 13px;

  font-size: 1px;
  background: url("../img/select2.png") right top no-repeat;
  outline: none;
}

html[dir="rtl"] .select2-search-choice-close {
  right: auto;
  left: 3px;
}

.select2-container-multi .select2-search-choice-close {
  left: 3px;
}

.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right -11px;
}

.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
  background-position: right -11px;
}

/* disabled styles */
.select2-container-multi.select2-container-disabled .select2-choices {
  cursor: default;
  background-color: #f4f4f4;
  background-image: none;
  border: 1px solid #ddd;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
  padding: 3px 5px;
  background-color: #f4f4f4;
  background-image: none;
  border: 1px solid #ddd;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
  display: none;
  background: none;
}

/* end multiselect */


.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-match {
  text-decoration: underline;
}

.select2-offscreen,
.select2-offscreen:focus {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
  clip: rect(0 0 0 0) !important;
  border: 0 !important;
  outline: 0 !important;
}

.select2-display-none {
  display: none;
}

.select2-measure-scrollbar {
  position: absolute;
  top: -10000px;
  left: -10000px;
  width: 100px;
  height: 100px;
  overflow: scroll;
}

/* Retina-ize icons */

// stylelint-disable-next-line
@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 2dppx)  {
  .select2-search input,
  .select2-search-choice-close,
  .select2-container .select2-choice abbr,
  .select2-container .select2-choice .select2-arrow b {
    background-image: url("../img/select2x2.png") !important;
    background-repeat: no-repeat !important;
    // stylelint-disable-next-line
    background-size: 60px 40px !important;
  }

  .select2-search input {
    background-position: 100% -21px !important;
  }
}


.form-control .select2-choice {
  border: 0;
  border-radius: 2px;
}

.form-control .select2-choice .select2-arrow {
  border-radius: 0 2px 2px 0;
}

.form-control.select2-container {
  height: auto !important;
  padding: 0;
}

.form-control.select2-container.select2-dropdown-open {
  border-radius: 3px 3px 0 0;
}

.form-control .select2-container.select2-dropdown-open .select2-choices {
  border-radius: 3px 3px 0 0;
}

.form-control.select2-container .select2-choices {
  border: 0 !important;
  border-radius: 3px;
}

.control-group.warning .select2-container .select2-choice,
.control-group.warning .select2-container .select2-choices,
.control-group.warning .select2-container-active .select2-choice,
.control-group.warning .select2-container-active .select2-choices,
.control-group.warning .select2-dropdown-open.select2-drop-above .select2-choice,
.control-group.warning .select2-dropdown-open.select2-drop-above .select2-choices,
.control-group.warning .select2-container-multi.select2-container-active .select2-choices {
  border: 1px solid #c09853 !important;
}

.control-group.warning .select2-container .select2-choice div {
  background: #fcf8e3 !important;
  border-left: 1px solid #c09853 !important;
}

.control-group.error .select2-container .select2-choice,
.control-group.error .select2-container .select2-choices,
.control-group.error .select2-container-active .select2-choice,
.control-group.error .select2-container-active .select2-choices,
.control-group.error .select2-dropdown-open.select2-drop-above .select2-choice,
.control-group.error .select2-dropdown-open.select2-drop-above .select2-choices,
.control-group.error .select2-container-multi.select2-container-active .select2-choices {
  border: 1px solid #b94a48 !important;
}

.control-group.error .select2-container .select2-choice div {
  background: #f2dede !important;
  border-left: 1px solid #b94a48 !important;
}

.control-group.info .select2-container .select2-choice,
.control-group.info .select2-container .select2-choices,
.control-group.info .select2-container-active .select2-choice,
.control-group.info .select2-container-active .select2-choices,
.control-group.info .select2-dropdown-open.select2-drop-above .select2-choice,
.control-group.info .select2-dropdown-open.select2-drop-above .select2-choices,
.control-group.info .select2-container-multi.select2-container-active .select2-choices {
  border: 1px solid #3a87ad !important;
}

.control-group.info .select2-container .select2-choice div {
  background: #d9edf7 !important;
  border-left: 1px solid #3a87ad !important;
}

.control-group.success .select2-container .select2-choice,
.control-group.success .select2-container .select2-choices,
.control-group.success .select2-container-active .select2-choice,
.control-group.success .select2-container-active .select2-choices,
.control-group.success .select2-dropdown-open.select2-drop-above .select2-choice,
.control-group.success .select2-dropdown-open.select2-drop-above .select2-choices,
.control-group.success .select2-container-multi.select2-container-active .select2-choices {
  border: 1px solid #468847 !important;
}

.control-group.success .select2-container .select2-choice div {
  background: #dff0d8 !important;
  border-left: 1px solid #468847 !important;
}
