@charset "UTF-8";

//Compass is required to pre-compile this stylesheets to .css
@import "~bourbon/app/assets/stylesheets/bourbon";

@import "modules/colors";
@import "modules/variables";
@import "modules/mixins";

@import "vendor/bi-app/bi-app-ltr";

@import "vendor/animate";
@import "vendor/font-awesome/font-awesome";

//Bootstrap Core : Variables + Mixins + Reset + Print
@import "vendor/bootstrap-sass/variables";
@import "vendor/bootstrap-sass/mixins";
@import "vendor/bootstrap-sass/normalize";

//import fonts
@import "~open-sans-fonts/open-sans-regular";
@import "~open-sans-fonts/open-sans-semibold";
@import "~open-sans-fonts/open-sans-bold";

//Bootstrap's rules applied only on div#content.bootstrap, nav.bootstrap, header.bootstrap, footer.bootstrap
.bootstrap {	//Core CSS
  @import "vendor/bootstrap-sass/scaffolding";
  @import "vendor/bootstrap-sass/type";
  @import "vendor/bootstrap-sass/code";
  @import "vendor/bootstrap-sass/grid";
  @import "vendor/bootstrap-sass/tables";
  @import "vendor/bootstrap-sass/forms";
  @import "vendor/bootstrap-sass/buttons";
  //Components
  @import "vendor/bootstrap-sass/component-animations";
  @import "vendor/bootstrap-sass/dropdowns";
  @import "vendor/bootstrap-sass/button-groups";
  @import "vendor/bootstrap-sass/input-groups";
  @import "vendor/bootstrap-sass/navs";
  @import "vendor/bootstrap-sass/navbar";
  @import "vendor/bootstrap-sass/breadcrumbs";
  @import "vendor/bootstrap-sass/pagination";
  @import "vendor/bootstrap-sass/pager";
  @import "vendor/bootstrap-sass/labels";
  @import "vendor/bootstrap-sass/badges";
  @import "vendor/bootstrap-sass/thumbnails";
  @import "vendor/bootstrap-sass/alerts";
  @import "vendor/bootstrap-sass/progress-bars";
  @import "vendor/bootstrap-sass/media";
  @import "vendor/bootstrap-sass/list-group";
  @import "vendor/bootstrap-sass/panels";
  @import "vendor/bootstrap-sass/wells";
  @import "vendor/bootstrap-sass/close";
  // Components w/ JavaScript
  @import "vendor/bootstrap-sass/tooltip";
  @import "vendor/bootstrap-sass/popovers";
  //Utility classes
  @import "vendor/bootstrap-sass/utilities";
  @import "vendor/bootstrap-sass/responsive-utilities";
}

//Moved out from .bootstrap prefix since modal's javascript appends the background overlay just before /body.
@import "vendor/bootstrap-sass/modals";
@import "vendor/bootstrap-sass/carousel";

//PrestaShop Elements
@import "partials/content";
@import "partials/header";
@import "partials/nav";
@import "partials/icons";

// stylelint-disable-next-line
.bootstrap {	//Partials
  @import "partials/commons";
  @import "partials/tables";
  @import "partials/tree";
  @import "partials/forms";
  @import "partials/kpi";
  @import "partials/switch-btn";
  @import "partials/toolbar";
  @import "partials/date-range-picker";
  @import "partials/multistore";
  @import "partials/product";
  @import "partials/buttons";

  //Controllers
  @import "controllers/carrier-wizard";
  @import "controllers/dashboard";
  @import "controllers/login";
  @import "controllers/modules";
  @import "controllers/search";
  @import "controllers/translations";
  @import "controllers/customer-thread";
  @import "controllers/themes";
  @import "controllers/order";
  @import "controllers/products";

}
@import "controllers/cartrules";

//Plug-ins
@import "partials/tinymce";
@import "partials/growl";
@import "partials/chosen";
@import "partials/ladda";
@import "partials/date-picker";
@import "partials/select2";

//Backward compatibility
@import "partials/backward";

//Print support
@import "partials/print";
