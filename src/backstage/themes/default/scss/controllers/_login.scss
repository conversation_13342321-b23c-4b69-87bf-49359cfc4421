#login {
  min-height: 100%;
  padding-bottom: 45px;
}

#shop-img {
  position: absolute;
  right: 0;
  left: 0;
  z-index: 1;
  width: 69.5px;
  margin: 0 auto;
}

#login-header {
  padding-top: 40px;
  margin-bottom: 30px;
  color: #6d6d6d;

  h1,
  h4 {
    padding: 0;
    margin: 0;
  }
}

#login-panel {
  width: 500px;
  margin: 0 auto;

  .form-control {
    height: inherit !important;
    padding: 10px 8px !important;
  }
  @media (max-width: $screen-phone) {
    width: 90%;
  }

  .panel {
    @include border-radius(0);
    @include box-shadow(0 1px 3px rgba(0,0,0,0.3));
  }

  .panel-footer {
    height: inherit;
    margin: 0 -20px -20px;
  }

  .flip-container {
    margin-top: 115px;
    @include perspective(1000px);

    &.flip {
      .flipper {
        @include rotateY(180deg);
      }

      .back {
        @include backface-visibility(visible);
      }
    }
  }

  .flipper {
    position: relative;
    @include transition-duration(0.6s);
    @include transform-style ();
  }

  .front,
  .back {
    width: 100%;
    padding: 40px;
    transition: 0.6s;
    @include backface-visibility(hidden);
  }

  .front {
    @include rotateY(0);
    z-index: 2;

    .form-group {
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .back {
    z-index: 1;
    display: none;
    @include rotateY(180deg);
  }

  #shop_name,
  #reset_name,
  #reset_confirm_name,
  #forgot_name,
  #forgot_confirm_name {
    font-family: $font-family-sans-serif;
    text-align: center;
  }

  #login_form {
    padding-top: 15px;
  }

  #remind-me {
    margin-top: 0;
  }
}

#login-footer {
  margin-top: 20px;

  a {
    color: #a0aab5;
  }
}

.login-back {
  position: fixed;
  top: 1rem;
  left: 1rem;
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 100;
  color: inherit;

  i {
    margin-top: -0.075rem;
    margin-right: 0.5rem;
    font-size: 1.4rem;
    vertical-align: middle;
  }

  &-shop {
    margin-left: 0.25rem;
    color: $link-color;
  }

  &:hover,
  &:focus {
    color: inherit;
    text-decoration: none;

    .login-back-shop {
      text-decoration: underline;
    }
  }

  span {
    @media screen and (max-width: $screen-phone) {
      display: none;
    }
  }
}
