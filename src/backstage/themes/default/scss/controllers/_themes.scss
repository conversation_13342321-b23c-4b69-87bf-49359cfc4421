.theme-container {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  background-color: rgba(#4d576e, 0.3);
  border: solid 1px #ccc;
  @include border-radius(3px);

  .theme-title {
    height: 40px;
    padding: 0;
    margin: 0 0 10px;
    font-size: 16px;
    line-height: 40px;
    color: #fff;
    text-align: center;
    background-color: #4d576e;
    border-bottom: solid 1px #fff;
  }

  .thumbnail-wrapper {
    .action-wrapper {
      position: absolute;
      top: 40px;
      left: 0;
      display: none;

      .action-overlay {
        width: 100%;
        height: 260px;
        @include background-image(linear-gradient(rgba(#4D576E,0.5),#4D576E));
      }

      .action-buttons {
        position: absolute;
        top: 130px;
        width: 100%;
        text-align: center;
      }
    }
  }
}

.addons-see-all-themes {
  padding-top: 21px;

  a {
    margin-left: 10px;
  }
}

.addons-style-search-bar {
  padding-top: 0;
}

&#content #psthemecusto {
  @media (max-width: $screen-xs) {
    .panel .panel-heading {
      display: flex;
      flex-wrap: wrap;
      padding-top: 5rem;

      .btn.btn-primary {
        margin: 0.5rem;
      }
    }
  }
}
