// ------------------------------------------
// bi app mixins
// authors: 
// twitter.com/anasnakawa
// twitter.com/victorzamfir
// licensed under the MIT license 
// http://www.opensource.org/licenses/mit-license.php
// ------------------------------------------

// ------------------------------------------
// Table of contents
// ------------------------------------------
// padding
// margin
// float
// text align
// clear
// left / right
// border
//  - width
//  - style
//  - color
//  - generic
//  - radius
// ltr / rtl contents
// ------------------------------------------

// generic mixin for properties with values
// (top right bottom left)
// ------------------------------------------
@mixin bi-app-compact($property, $top, $right, $bottom, $left) {
  @if $bi-app-direction == ltr {
    #{$property}: $top $right $bottom $left;
  } @else {
    #{$property}: $top $left $bottom $right;
  }
}

// padding
// ------------------------------------------
@mixin padding-left($distance) {
  padding-#{$bi-app-left}: $distance;
}

@mixin padding-right($distance) {
  padding-#{$bi-app-right}: $distance;
}

@mixin padding($top, $right, $bottom, $left) {
  @include bi-app-compact(padding, $top, $right, $bottom, $left);
}

// margin
// ------------------------------------------
@mixin margin-left($distance) {
  margin-#{$bi-app-left}: $distance;
}

@mixin margin-right($distance) {
  margin-#{$bi-app-right}: $distance;
}

@mixin margin($top, $right, $bottom, $left) {
  @include bi-app-compact(margin, $top, $right, $bottom, $left);
}

// float
// ------------------------------------------
@mixin bi-app-float-left {
  float: $bi-app-left;
}

@mixin bi-app-float-right {
  float: $bi-app-right;
}

@mixin float($direction) {
  @if $direction == left {
    @include bi-app-float-left;
  } @else if $direction == right {
    @include bi-app-float-right;
  } @else {
    float: $direction;
  }
}

// text align
// ------------------------------------------
@mixin bi-app-text-align-left {
  text-align: $bi-app-left;
}

@mixin bi-app-text-align-right {
  text-align: $bi-app-right;
}

@mixin text-align($direction) {
  @if $direction == left {
    @include bi-app-text-align-left;
  } @else if $direction == right {
    @include bi-app-text-align-right;
  } @else {
    text-align: $direction;
  }
}

// clear
// ------------------------------------------
@mixin bi-app-clear-left {
  clear: $bi-app-left;
}

@mixin bi-app-clear-right {
  clear: $bi-app-right;
}

@mixin clear($direction) {
  @if $direction == left {
    @include bi-app-clear-left;
  } @else if $direction == right {
    @include bi-app-clear-right;
  } @else {
    clear: $direction;
  }
}

// left / right
// ------------------------------------------
@mixin left($distance) {
  @if $bi-app-direction == ltr {
    left: $distance;
  } @else if $bi-app-direction == rtl {
    right: $distance;
  }
}

@mixin right($distance) {
  @if $bi-app-direction == ltr {
    right: $distance;
  } @else if $bi-app-direction == rtl {
    left: $distance;
  }
}

// border
// ------------------------------------------

// width
@mixin border-left-width($width) {
  border-#{$bi-app-left}-width: $width;
}

@mixin border-right-width($width) {
  border-#{$bi-app-right}-width: $width;
}

@mixin border-width($top, $right, $bottom, $left) {
  @include bi-app-compact(border-width, $top, $right, $bottom, $left);
}

// style
@mixin border-left-style($style) {
  border-#{$bi-app-left}-style: $style;
}

@mixin border-right-style($style) {
  border-#{$bi-app-right}-style: $style;
}

@mixin border-style($top, $right, $bottom, $left) {
  @include bi-app-compact(border-style, $top, $right, $bottom, $left);
}

// color
@mixin border-left-color($color) {
  border-#{$bi-app-left}-color: $color;
}

@mixin border-right-color($color) {
  border-#{$bi-app-right}-color: $color;
}

@mixin border-color($top, $right, $bottom, $left) {
  @include bi-app-compact(border-color, $top, $right, $bottom, $left);
}

// generic
@mixin border-left($border-style) {
  border-#{$bi-app-left}: $border-style;
}

@mixin border-right($border-style) {
  border-#{$bi-app-right}: $border-style;
}

// radius
@mixin border-top-left-radius($radius) {
  -webkit-border-top-#{$bi-app-left}-radius: $radius;
          border-top-#{$bi-app-left}-radius: $radius;
}

@mixin border-top-right-radius($radius) {
  -webkit-border-top-#{$bi-app-right}-radius: $radius;
          border-top-#{$bi-app-right}-radius: $radius;
}

@mixin border-bottom-left-radius($radius) {
  -webkit-border-bottom-#{$bi-app-left}-radius: $radius;
          border-bottom-#{$bi-app-left}-radius: $radius; 
}

@mixin border-bottom-right-radius($radius) {
  -webkit-border-bottom-#{$bi-app-right}-radius: $radius;
          border-bottom-#{$bi-app-right}-radius: $radius;
}

@mixin border-right-radius($radius) {
  @include border-top-right-radius($radius);
  @include border-bottom-right-radius($radius);
}

@mixin border-left-radius($radius) {
  @include border-top-left-radius($radius);
  @include border-bottom-left-radius($radius);
}

@mixin border-top-radius($radius) {
  @include border-top-left-radius($radius);
  @include border-top-right-radius($radius);
}

@mixin border-bottom-radius($radius) {
  @include border-bottom-left-radius($radius);
  @include border-bottom-right-radius($radius);
}

@mixin border-radius($topLeft, $topRight: null, $bottomRight: null, $bottomLeft: null) {
  @if $topRight != null {
    @include border-top-left-radius($topLeft);
    @include border-top-right-radius($topRight);
    @include border-bottom-right-radius($bottomRight);
    @include border-bottom-left-radius($bottomLeft);
  } @else {
    -webkit-border-radius: $topLeft;
            border-radius: $topLeft;
  }
}

// Returns "en" or "ar", useful for image suffixes.
// Usage: background-image: url(/img/header-#{lang()}.png);
@function lang() {
  @if $bi-app-direction == ltr {
    @return 'en';
  } @else {
    @return 'ar';
  }
}

// Support for "direction" declaration (renders ltr/rtl).
// Useful for form elements as they swap the text-indent property and align the text accordingly.
@mixin direction {
  direction: $bi-app-direction;
}

// Inverts a percentage value. Example: 97% becames 3%.
// Useful for background-position.
@function bi-app-invert-percentage($percentage) {
  @if $bi-app-direction == rtl {
    @return 100% - $percentage;
  } @else {
    @return $percentage;
  }
}

// ltr / rtl contents
// ------------------------------------------
@mixin ltr {
  @if $bi-app-direction == ltr {
    @content;
  }
}

@mixin rtl {
  @if $bi-app-direction == rtl {
    @content;
  }
}
