{"name": "glob-parent", "version": "3.1.0", "description": "Strips glob magic from a string to provide the parent directory path", "main": "index.js", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/es128/glob-parent"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "files": ["index.js"], "author": "<PERSON><PERSON> (https://github.com/es128)", "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}}