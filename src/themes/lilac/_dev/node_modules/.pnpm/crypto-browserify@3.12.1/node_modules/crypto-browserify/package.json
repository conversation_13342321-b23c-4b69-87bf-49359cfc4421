{"author": "<PERSON> <<EMAIL>> (dominictarr.com)", "name": "crypto-browserify", "description": "implementation of crypto for the browser", "version": "3.12.1", "homepage": "https://github.com/browserify/crypto-browserify", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/browserify/crypto-browserify.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "engines": {"node": ">= 0.10"}, "dependencies": {"browserify-cipher": "^1.0.1", "browserify-sign": "^4.2.3", "create-ecdh": "^4.0.4", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "diffie-hellman": "^5.0.3", "hash-base": "~3.0.4", "inherits": "^2.0.4", "pbkdf2": "^3.1.2", "public-encrypt": "^4.0.3", "randombytes": "^2.1.0", "randomfill": "^1.0.4"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "eslint": "=8.8.0", "hash-test-vectors": "^1.3.2", "nyc": "^10.3.2", "object.entries": "^1.1.8", "pseudorandombytes": "^2.0.0", "safe-buffer": "^5.2.1", "semver": "^6.3.1", "tape": "^5.9.0"}, "browser": {"crypto": false}, "license": "MIT"}