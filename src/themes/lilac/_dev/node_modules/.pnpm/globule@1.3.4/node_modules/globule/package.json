{"name": "globule", "description": "An easy-to-use wildcard globbing library.", "version": "1.3.4", "homepage": "https://github.com/cowboy/node-globule", "author": {"name": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/"}, "repository": {"type": "git", "url": "git://github.com/cowboy/node-globule.git"}, "bugs": {"url": "https://github.com/cowboy/node-globule/issues"}, "license": "MIT", "main": "lib/globule", "files": ["lib"], "engines": {"node": ">= 0.10"}, "scripts": {"test": "grunt"}, "devDependencies": {"grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "grunt": "^1.0.2"}, "keywords": ["glob", "file", "match", "mapping", "expand", "wildcard", "pattern", "sync", "awesome"], "dependencies": {"glob": "~7.1.1", "lodash": "^4.17.21", "minimatch": "~3.0.2"}}