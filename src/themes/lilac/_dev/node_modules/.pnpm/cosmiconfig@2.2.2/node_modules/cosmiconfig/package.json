{"name": "cosmiconfig", "version": "2.2.2", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "tape": "tape test/*.test.js | tap-spec", "coverage": "nyc npm run tape && nyc report --reporter=html && open coverage/index.html", "test": "npm run tape && npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.1.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "^3.13.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^3.0.5", "expect": "^1.20.2", "lodash": "^4.17.4", "node-version-check": "^2.1.1", "nyc": "^10.0.0", "sinon": "^1.17.7", "tap-spec": "^4.1.1", "tape": "^4.6.3"}, "engines": {"node": ">=0.12"}}