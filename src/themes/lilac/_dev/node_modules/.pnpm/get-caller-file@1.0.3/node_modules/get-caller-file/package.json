{"name": "get-caller-file", "version": "1.0.3", "description": "", "main": "index.js", "directories": {"test": "tests"}, "files": ["index.js"], "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0"}}