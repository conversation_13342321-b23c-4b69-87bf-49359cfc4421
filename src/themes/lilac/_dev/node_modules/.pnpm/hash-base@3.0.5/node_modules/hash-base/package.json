{"name": "hash-base", "version": "3.0.5", "description": "abstract base class for hash-streams", "keywords": ["hash", "stream"], "homepage": "https://github.com/crypto-browserify/hash-base", "bugs": {"url": "https://github.com/crypto-browserify/hash-base/issues"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/fanatid)", "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/hash-base.git"}, "scripts": {"lint": "standard", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx npm@'>=10.2' audit --production"}, "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "devDependencies": {"nyc": "^10.3.2", "standard": "^14.3.3", "tape": "^5.9.0"}, "engines": {"node": ">= 0.10"}}