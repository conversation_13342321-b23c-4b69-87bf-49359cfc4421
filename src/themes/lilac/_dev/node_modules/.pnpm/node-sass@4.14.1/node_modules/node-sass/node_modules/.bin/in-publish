#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/in-publish@2.0.1/node_modules/in-publish/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/in-publish@2.0.1/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/in-publish@2.0.1/node_modules/in-publish/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/in-publish@2.0.1/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../in-publish@2.0.1/node_modules/in-publish/in-publish.js" "$@"
else
  exec node  "$basedir/../../../../../in-publish@2.0.1/node_modules/in-publish/in-publish.js" "$@"
fi
