# for-own [![NPM version](https://img.shields.io/npm/v/for-own.svg?style=flat)](https://www.npmjs.com/package/for-own) [![NPM monthly downloads](https://img.shields.io/npm/dm/for-own.svg?style=flat)](https://npmjs.org/package/for-own)  [![NPM total downloads](https://img.shields.io/npm/dt/for-own.svg?style=flat)](https://npmjs.org/package/for-own) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/for-own.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/for-own)

> Iterate over the own enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save for-own
```

## Usage

```js
var forOwn = require('for-own');

var obj = {a: 'foo', b: 'bar', c: 'baz'};
var values = [];
var keys = [];

forOwn(obj, function (value, key, o) {
  keys.push(key);
  values.push(value);
});

console.log(keys);
//=> ['a', 'b', 'c'];

console.log(values);
//=> ['foo', 'bar', 'baz'];
```

## About

### Related projects

* [arr-flatten](https://www.npmjs.com/package/arr-flatten): Recursively flatten an array or arrays. This is the fastest implementation of array flatten. | [homepage](https://github.com/jonschlinkert/arr-flatten "Recursively flatten an array or arrays. This is the fastest implementation of array flatten.")
* [collection-map](https://www.npmjs.com/package/collection-map): Returns an array of mapped values from an array or object. | [homepage](https://github.com/jonschlinkert/collection-map "Returns an array of mapped values from an array or object.")
* [for-in](https://www.npmjs.com/package/for-in): Iterate over the own and inherited enumerable properties of an object, and return an object… [more](https://github.com/jonschlinkert/for-in) | [homepage](https://github.com/jonschlinkert/for-in "Iterate over the own and inherited enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 10 | [jonschlinkert](https://github.com/jonschlinkert) |
| 1 | [javiercejudo](https://github.com/javiercejudo) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.4.2, on February 26, 2017._