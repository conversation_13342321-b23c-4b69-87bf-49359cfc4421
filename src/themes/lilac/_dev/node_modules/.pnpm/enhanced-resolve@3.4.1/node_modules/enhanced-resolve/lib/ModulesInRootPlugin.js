/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/
function ModulesInRootPlugin(source, path, target) {
	this.source = source;
	this.path = path;
	this.target = target;
}
module.exports = ModulesInRootPlugin;

ModulesInRootPlugin.prototype.apply = function(resolver) {
	var target = this.target;
	var path = this.path;
	resolver.plugin(this.source, function(request, callback) {
		var obj = Object.assign({}, request, {
			path: path,
			request: "./" + request.request
		});
		resolver.doResolve(target, obj, "looking for modules in " + path, callback, true);
	});
};
