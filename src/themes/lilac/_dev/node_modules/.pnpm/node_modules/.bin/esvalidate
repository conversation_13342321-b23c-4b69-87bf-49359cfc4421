#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/esprima-fb@15001.1001.0-dev-harmony-fb/node_modules/esprima-fb/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/esprima-fb@15001.1001.0-dev-harmony-fb/node_modules/esprima-fb/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/esprima-fb@15001.1001.0-dev-harmony-fb/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/esprima-fb@15001.1001.0-dev-harmony-fb/node_modules/esprima-fb/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/esprima-fb@15001.1001.0-dev-harmony-fb/node_modules/esprima-fb/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/esprima-fb@15001.1001.0-dev-harmony-fb/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../esprima-fb/bin/esvalidate.js" "$@"
else
  exec node  "$basedir/../esprima-fb/bin/esvalidate.js" "$@"
fi
