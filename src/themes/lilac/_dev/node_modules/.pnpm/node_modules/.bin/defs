#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules/defs/build/es5/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules/defs/build/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules/defs/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules/defs/build/es5/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules/defs/build/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules/defs/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/defs@1.1.1/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../defs/build/es5/defs" "$@"
else
  exec node  "$basedir/../defs/build/es5/defs" "$@"
fi
