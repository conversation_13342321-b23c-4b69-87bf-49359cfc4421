#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/commoner@0.10.8/node_modules/commoner/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/commoner@0.10.8/node_modules/commoner/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/commoner@0.10.8/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/commoner@0.10.8/node_modules/commoner/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/commoner@0.10.8/node_modules/commoner/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/commoner@0.10.8/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../commoner/bin/commonize" "$@"
else
  exec node  "$basedir/../commoner/bin/commonize" "$@"
fi
