#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sha.js@2.4.11/node_modules/sha.js/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sha.js@2.4.11/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sha.js@2.4.11/node_modules/sha.js/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sha.js@2.4.11/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../sha.js/bin.js" "$@"
else
  exec node  "$basedir/../sha.js/bin.js" "$@"
fi
