#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/svgo@0.7.2/node_modules/svgo/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/svgo@0.7.2/node_modules/svgo/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/svgo@0.7.2/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/svgo@0.7.2/node_modules/svgo/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/svgo@0.7.2/node_modules/svgo/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/svgo@0.7.2/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../svgo/bin/svgo" "$@"
else
  exec node  "$basedir/../svgo/bin/svgo" "$@"
fi
