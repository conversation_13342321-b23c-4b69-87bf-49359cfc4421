#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sass-graph@2.2.5/node_modules/sass-graph/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sass-graph@2.2.5/node_modules/sass-graph/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sass-graph@2.2.5/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sass-graph@2.2.5/node_modules/sass-graph/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sass-graph@2.2.5/node_modules/sass-graph/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/sass-graph@2.2.5/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../sass-graph/bin/sassgraph" "$@"
else
  exec node  "$basedir/../sass-graph/bin/sassgraph" "$@"
fi
