#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/acorn@5.7.4/node_modules/acorn/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/acorn@5.7.4/node_modules/acorn/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/acorn@5.7.4/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/acorn@5.7.4/node_modules/acorn/bin/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/acorn@5.7.4/node_modules/acorn/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/acorn@5.7.4/node_modules:/data/baora/src/themes/lilac/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../acorn/bin/acorn" "$@"
else
  exec node  "$basedir/../acorn/bin/acorn" "$@"
fi
