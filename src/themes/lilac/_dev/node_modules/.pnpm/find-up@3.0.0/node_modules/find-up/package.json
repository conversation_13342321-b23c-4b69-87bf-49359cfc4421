{"name": "find-up", "version": "3.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": "sindresorhus/find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"locate-path": "^3.0.0"}, "devDependencies": {"ava": "*", "tempy": "^0.2.1", "xo": "*"}}