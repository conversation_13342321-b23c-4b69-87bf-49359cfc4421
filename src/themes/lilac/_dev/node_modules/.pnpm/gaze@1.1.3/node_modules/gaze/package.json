{"name": "gaze", "description": "A globbing fs.watch wrapper built from the best parts of other fine watch libs.", "version": "1.1.3", "homepage": "https://github.com/shama/gaze", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/shama/gaze.git"}, "bugs": {"url": "https://github.com/shama/gaze/issues"}, "license": "MIT", "main": "lib/gaze", "engines": {"node": ">= 4.0.0"}, "scripts": {"test": "semistandard && grunt nodeunit -v"}, "dependencies": {"globule": "^1.0.0"}, "devDependencies": {"async": "^2.6.1", "grunt": "^1.0.1", "grunt-benchmark": "^1.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-jshint": "^1.1.0", "grunt-contrib-nodeunit": "^2.0.0", "rimraf": "^2.5.2", "semistandard": "^12.0.1"}, "keywords": ["watch", "glob"], "files": ["lib", "LICENSE-MIT"], "semistandard": {"ignore": ["benchmarks", "experiments", "build", "test"]}}