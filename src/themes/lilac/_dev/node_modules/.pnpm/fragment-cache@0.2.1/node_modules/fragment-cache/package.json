{"name": "fragment-cache", "description": "A cache for managing namespaced sub-caches", "version": "0.2.1", "homepage": "https://github.com/jonschlinkert/fragment-cache", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/fragment-cache", "bugs": {"url": "https://github.com/jonschlinkert/fragment-cache/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"map-cache": "^0.2.2"}, "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.11", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "mocha": "^3.2.0"}, "keywords": ["cache", "fragment"], "verb": {"plugins": ["gulp-format-md"], "reflinks": ["map-cache", "verb"], "related": {"list": ["base", "map-cache"]}, "layout": "default", "toc": false, "tasks": ["readme"], "lint": {"reflinks": true}}}