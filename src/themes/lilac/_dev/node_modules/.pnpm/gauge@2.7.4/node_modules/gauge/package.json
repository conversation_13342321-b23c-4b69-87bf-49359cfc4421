{"name": "gauge", "version": "2.7.4", "description": "A terminal based horizontal guage", "main": "index.js", "scripts": {"test": "standard && tap test/*.js --coverage", "prepublish": "rm -f *~"}, "repository": {"type": "git", "url": "https://github.com/iarna/gauge"}, "keywords": ["progressbar", "progress", "gauge"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "homepage": "https://github.com/iarna/gauge", "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}, "devDependencies": {"readable-stream": "^2.0.6", "require-inject": "^1.4.0", "standard": "^7.1.2", "tap": "^5.7.2", "through2": "^2.0.0"}, "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"]}