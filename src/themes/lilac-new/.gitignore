# Lilac New Theme - Git Ignore File

# =============================================================================
# Node.js Dependencies
# =============================================================================
_dev/node_modules/
_dev/npm-debug.log*
_dev/yarn-debug.log*
_dev/yarn-error.log*
_dev/lerna-debug.log*
_dev/.npm
_dev/.yarn-integrity

# =============================================================================
# Build Output
# =============================================================================
# Vite build outputs
assets/css/theme.css
assets/js/theme.js
assets/js/*.js
assets/css/*.css
assets/manifest.json
assets/.vite/

# Legacy webpack outputs (if any remain)
assets/cache/
assets/js/theme.min.js
assets/css/theme.min.css

# =============================================================================
# Development Files
# =============================================================================
# Vite cache
_dev/.vite/
_dev/dist/

# Development server files
_dev/.env
_dev/.env.local
_dev/.env.development.local
_dev/.env.test.local
_dev/.env.production.local

# Hot reload files
_dev/.hot-update.*

# =============================================================================
# IDE and Editor Files
# =============================================================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Operating System Files
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Logs and Debug Files
# =============================================================================
*.log
logs/
*.pid
*.seed
*.pid.lock
.grunt
lib-cov
coverage/
.nyc_output
.coverage
*.lcov
.sass-cache/
connect.lock
typings/

# =============================================================================
# Temporary Files
# =============================================================================
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*.orig
*.rej

# =============================================================================
# Compiled Files
# =============================================================================
*.com
*.class
*.dll
*.exe
*.o
*.so

# =============================================================================
# Package Files
# =============================================================================
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# =============================================================================
# PrestaShop Specific
# =============================================================================
# PrestaShop cache files
cache/
var/cache/

# PrestaShop logs
var/logs/

# PrestaShop configuration (if accidentally copied)
config/settings.inc.php
config/config.inc.php

# PrestaShop install files
install/
install-dev/

# =============================================================================
# Theme Specific
# =============================================================================
# Compiled assets that should be built
assets/js/theme-*.js
assets/css/theme-*.css

# Source maps (optional - uncomment if you don't want to track them)
# assets/js/*.map
# assets/css/*.map

# Image optimization cache
assets/img/.cache/

# Font cache
assets/fonts/.cache/

# =============================================================================
# Development Tools
# =============================================================================
# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# =============================================================================
# Security and Sensitive Files
# =============================================================================
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# API keys and secrets
secrets.json
.secrets

# =============================================================================
# Testing
# =============================================================================
# Jest coverage
coverage/
.coverage/

# Cypress
cypress/videos/
cypress/screenshots/

# =============================================================================
# Documentation Build
# =============================================================================
# Generated documentation
docs/build/
docs/.vuepress/dist/

# =============================================================================
# Miscellaneous
# =============================================================================
# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
