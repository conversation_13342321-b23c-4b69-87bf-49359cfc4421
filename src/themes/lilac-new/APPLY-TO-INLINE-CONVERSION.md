# @apply to Inline Classes Conversion Guide

This document outlines the conversion of `@apply` directives to inline Tailwind classes in Vue components.

## ✅ **Completed Conversions**

### **1. DropDown.vue** ✅
- **Before**: Used `@apply` directives in `<style scoped>` section
- **After**: All styles moved to inline classes in template
- **Changes**:
  - `.dropdown` → `class="relative inline-block"`
  - `.dropdown-toggle` → `class="flex items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary"`
  - `.dropdown-menu` → Dynamic classes with `:class` binding for show/hide states
  - **Style section**: Completely removed

### **2. TopMenu.vue** ✅
- **Before**: Complex `@apply` directives with responsive styles
- **After**: All styles converted to inline classes with dynamic bindings
- **Changes**:
  - Menu items use dynamic classes based on state (mobile/desktop, active, etc.)
  - Responsive behavior handled through `:class` bindings
  - Mobile menu toggle converted to inline classes
  - **Style section**: Completely removed

### **3. Modal.vue** ✅
- **Before**: Extensive `@apply` directives for modal components
- **After**: Inline classes with size variants handled dynamically
- **Changes**:
  - Modal overlay and container converted to inline classes
  - Size variants (`sm`, `md`, `lg`, `xl`) handled with dynamic `:class` binding
  - **Style section**: Kept only animations and keyframes (no `@apply`)

### **4. CartQuantity.vue** ✅
- **Before**: Form controls and buttons with `@apply` directives
- **After**: All form styling moved to inline classes
- **Changes**:
  - Input group styling converted to flex classes
  - Button states handled with dynamic classes
  - Error states managed through `:class` bindings
  - **Style section**: Completely removed

## 🔄 **Remaining Components to Convert**

### **5. ProductMiniature.vue** 🔄
**Location**: `src/themes/lilac-new/_dev/src/components/ProductMiniature.vue`

**Current @apply usage**:
```css
.product-miniature {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
}
.thumbnail-container {
  @apply relative;
}
.product-image {
  @apply w-full h-auto object-cover transition-transform duration-300 hover:scale-105;
}
// ... and many more
```

**Conversion needed**:
- Move all `@apply` styles to `class` attributes
- Handle hover states with `hover:` prefixes
- Remove `<style scoped>` section

### **6. ProductSelect.vue** 🔄
**Location**: `src/themes/lilac-new/_dev/src/components/ProductSelect.vue`

### **7. BlockCart.vue** 🔄
**Location**: `src/themes/lilac-new/_dev/src/components/BlockCart.vue`

### **8. FormHandler.vue** 🔄
**Location**: `src/themes/lilac-new/_dev/src/components/FormHandler.vue`

### **9. PromoCode.vue** 🔄
**Location**: `src/themes/lilac-new/_dev/src/components/PromoCode.vue`

### **10. PasswordToggle.vue** 🔄
**Location**: `src/themes/lilac-new/_dev/src/components/PasswordToggle.vue`

### **11. SearchWidget.vue** 🔄
**Location**: `src/themes/lilac-new/_dev/src/components/SearchWidget.vue`

## 📋 **Conversion Process**

### **Step 1: Identify @apply Directives**
```bash
# Find all Vue files with @apply
grep -r "@apply" src/themes/lilac-new/_dev/src/components/
```

### **Step 2: Convert Each Component**
For each component:

1. **Analyze the template structure**
2. **Map @apply classes to inline classes**
3. **Handle dynamic states with :class bindings**
4. **Remove or simplify the style section**

### **Step 3: Common Conversion Patterns**

#### **Basic Layout Classes**
```css
/* Before */
.element {
  @apply flex items-center justify-between p-4 bg-white border rounded-lg;
}
```

```vue
<!-- After -->
<div class="flex items-center justify-between p-4 bg-white border rounded-lg">
```

#### **Hover States**
```css
/* Before */
.button {
  @apply bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-700;
}
```

```vue
<!-- After -->
<button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-700">
```

#### **Dynamic States**
```css
/* Before */
.modal {
  @apply opacity-0 invisible;
}
.modal.show {
  @apply opacity-100 visible;
}
```

```vue
<!-- After -->
<div 
  class="transition-all duration-200"
  :class="{
    'opacity-100 visible': isVisible,
    'opacity-0 invisible': !isVisible
  }"
>
```

#### **Responsive Classes**
```css
/* Before */
@media (max-width: 767px) {
  .menu {
    @apply flex-col w-full;
  }
}
```

```vue
<!-- After -->
<div class="flex md:flex-row flex-col w-full md:w-auto">
```

## 🎯 **Benefits of Inline Classes**

### **1. Better Performance**
- No CSS-in-JS processing overhead
- Direct Tailwind utility application
- Smaller bundle sizes

### **2. Improved Developer Experience**
- Classes visible directly in template
- Better IDE autocomplete
- Easier debugging

### **3. Simplified Build Process**
- No `@apply` directive processing
- Faster compilation
- Cleaner CSS output

## ⚠️ **Important Notes**

### **1. Keep Animations**
Some CSS should remain in `<style>` sections:
- `@keyframes` animations
- Complex transitions
- Global styles

### **2. Handle Complex States**
Use Vue's `:class` binding for:
- Component state changes
- Conditional styling
- Dynamic classes

### **3. Maintain Accessibility**
Ensure all accessibility classes are preserved:
- `sr-only` for screen readers
- Focus states
- ARIA attributes

## 🚀 **Next Steps**

1. **Complete remaining components** using the patterns above
2. **Test all components** to ensure styling is preserved
3. **Remove unused CSS** from style sections
4. **Verify responsive behavior** across breakpoints
5. **Test accessibility** features

## 📚 **Reference**

### **Common Class Mappings**
- `@apply flex` → `class="flex"`
- `@apply items-center` → `class="items-center"`
- `@apply justify-between` → `class="justify-between"`
- `@apply p-4` → `class="p-4"`
- `@apply bg-white` → `class="bg-white"`
- `@apply border` → `class="border"`
- `@apply rounded-lg` → `class="rounded-lg"`
- `@apply hover:bg-gray-100` → `class="hover:bg-gray-100"`

### **Dynamic Class Patterns**
```vue
<!-- Conditional classes -->
:class="{ 'active-class': isActive }"

<!-- Multiple conditions -->
:class="{
  'class-1': condition1,
  'class-2': condition2
}"

<!-- Computed classes -->
:class="computedClasses"
```

The conversion improves performance, maintainability, and aligns with Tailwind CSS best practices!
