# Lilac New - Modern PrestaShop Theme

A modern PrestaShop theme built with **Tailwind CSS**, **Vue 3**, and **Vite** - converted from the original Bootstrap/jQuery/Webpack-based Lilac theme.

## 🚀 Features

- **Modern Build System**: Vite for fast development and optimized production builds
- **Vue 3**: Component-based architecture with Composition API
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development
- **TypeScript Ready**: Full TypeScript support (optional)
- **Hot Module Replacement**: Instant updates during development
- **Optimized Assets**: Automatic code splitting and optimization
- **PrestaShop Compatible**: Maintains full compatibility with PrestaShop 1.7+

## 📋 Requirements

- Node.js 18+ 
- npm or yarn
- PrestaShop ******* or higher

## 🛠️ Installation

1. **Copy the theme** to your PrestaShop themes directory:
   ```bash
   cp -r lilac-new /path/to/prestashop/themes/
   ```

2. **Install dependencies**:
   ```bash
   cd /path/to/prestashop/themes/lilac-new/_dev
   npm install
   ```

3. **Activate the theme** in your PrestaShop back office:
   - Go to Design > Theme & Logo
   - Select "Lilac New (Tailwind + Vue)" theme
   - Click "Use this theme"

## 🔧 Development

### Development Mode

Start the development server with hot reload:

```bash
cd _dev
npm run dev
```

This will:
- Start Vite dev server on `http://localhost:3000`
- Enable hot module replacement for both JS and CSS
- Automatically inject CSS via JavaScript (no separate CSS file needed)
- Load assets from the dev server
- Provide instant feedback on changes
- Show development mode indicator in browser

### Production Build

Build optimized assets for production:

```bash
cd _dev
npm run build
```

This will:
- Generate optimized CSS and JavaScript files
- Output files to `../assets/` directory
- Enable all optimizations (minification, tree-shaking, etc.)

### Watch Mode

Build and watch for changes:

```bash
cd _dev
npm run watch
```

## 📁 Project Structure

```
lilac-new/
├── _dev/                          # Development files
│   ├── src/                       # Source files
│   │   ├── components/            # Vue components
│   │   │   ├── DropDown.vue
│   │   │   ├── TopMenu.vue
│   │   │   ├── ProductMiniature.vue
│   │   │   ├── ProductSelect.vue
│   │   │   ├── BlockCart.vue
│   │   │   └── FormHandler.vue
│   │   ├── modules/               # Page-specific modules
│   │   │   ├── responsive.js
│   │   │   ├── cart.js
│   │   │   ├── product.js
│   │   │   ├── checkout.js
│   │   │   ├── customer.js
│   │   │   └── listing.js
│   │   ├── utils/                 # Utility functions
│   │   │   └── prestashop.js
│   │   ├── main.js               # Main JavaScript entry
│   │   └── main.css              # Main CSS entry (Tailwind)
│   ├── package.json              # Dependencies
│   ├── vite.config.js            # Vite configuration
│   ├── tailwind.config.js        # Tailwind configuration
│   └── postcss.config.js         # PostCSS configuration
├── assets/                       # Built assets (generated)
│   ├── css/
│   │   └── theme.css
│   └── js/
│       └── theme.js
├── templates/                    # PrestaShop templates
├── config/                       # Theme configuration
└── README.md                     # This file
```

## 🎨 Customization

### Tailwind CSS

Customize the design system in `_dev/tailwind.config.js`:

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        'brand-primary': '#007cba',
        // Add your custom colors
      },
      fontFamily: {
        'sans': ['Your Font', 'sans-serif']
      }
    }
  }
}
```

### Vue Components

Create new Vue components in `_dev/src/components/`:

```vue
<template>
  <div class="my-component">
    <!-- Your template -->
  </div>
</template>

<script>
export default {
  name: 'MyComponent',
  // Component logic
}
</script>

<style scoped>
/* Component-specific styles */
</style>
```

### Adding New Modules

Create page-specific functionality in `_dev/src/modules/`:

```javascript
// _dev/src/modules/my-module.js
class MyModule {
  constructor() {
    this.init()
  }

  init() {
    // Module initialization
  }
}

export default new MyModule()
```

Then import it in `_dev/src/main.js`:

```javascript
import './modules/my-module'
```

## 🔌 PrestaShop Integration

### Event System

The theme provides a comprehensive event system for PrestaShop integration:

```javascript
import { prestashopEventBus, PRESTASHOP_EVENTS } from './utils/prestashop'

// Listen for cart updates
prestashopEventBus.on(PRESTASHOP_EVENTS.CART_UPDATED, (data) => {
  console.log('Cart updated:', data)
})

// Emit custom events
prestashopEventBus.emit('customEvent', { data: 'value' })
```

### Utility Functions

Access PrestaShop data and functionality:

```javascript
import { prestashopUtils } from './utils/prestashop'

// Get configuration values
const currency = prestashopUtils.getConfig('currency_iso_code')

// Get customer info
const customer = prestashopUtils.getCustomerInfo()

// Make AJAX requests with proper tokens
const response = await prestashopUtils.makeRequest('/cart', {
  method: 'POST',
  body: formData
})
```

## 🚀 Performance

The theme is optimized for performance:

- **Code Splitting**: Automatic splitting of vendor and application code
- **Tree Shaking**: Removes unused code from the final bundle
- **Asset Optimization**: Images, fonts, and other assets are optimized
- **Lazy Loading**: Components and routes can be loaded on demand
- **Caching**: Proper cache headers for static assets

## 🐛 Troubleshooting

### Development Server Issues

If the development server doesn't start:

1. Check if port 3000 is available
2. Ensure Node.js version is 18+
3. Clear node_modules and reinstall: `rm -rf node_modules && npm install`

### Build Issues

If the build fails:

1. Check for TypeScript errors (if using TypeScript)
2. Ensure all imports are correct
3. Check the browser console for runtime errors

### PrestaShop Integration Issues

If Vue components don't initialize:

1. Check browser console for JavaScript errors
2. Ensure PrestaShop variables are properly loaded
3. Verify template files are correctly updated

## 📝 Migration Notes

This theme maintains compatibility with the original Lilac theme while providing modern development tools:

- **Templates**: All original template functionality is preserved
- **Hooks**: All PrestaShop hooks continue to work
- **Modules**: Module compatibility is maintained
- **Styling**: Visual appearance closely matches the original theme

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This theme is licensed under the Academic Free License 3.0 (AFL-3.0), same as PrestaShop.

## 🆘 Support

For support and questions:

- Check the [PrestaShop documentation](https://devdocs.prestashop.com/)
- Review the [Vue.js documentation](https://vuejs.org/)
- Consult the [Tailwind CSS documentation](https://tailwindcss.com/)
- Check the [Vite documentation](https://vitejs.dev/)
