{"name": "prestashop-lilac-new-dev-tools", "version": "2.0.0", "description": "Modern development tools for Lilac New theme with Vite, Vue, and Tailwind CSS", "type": "module", "scripts": {"dev": "vite", "build": "npm run extract-classes && vite build", "preview": "vite preview", "watch": "vite build --watch", "extract-classes": "node scripts/extract-classes.js", "build:css": "npm run extract-classes && tailwindcss -i src/main.css -o ../assets/css/theme.css --watch"}, "author": "Cetus Technology", "license": "AFL-3.0", "dependencies": {"vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.0", "glob": "^10.3.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "vite": "^6.3.5", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@tailwindcss/aspect-ratio": "^0.4.0"}}