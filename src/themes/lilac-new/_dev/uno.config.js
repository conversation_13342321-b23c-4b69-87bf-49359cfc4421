import {
    defineConfig,
    presetWind4,
    presetIcons,
    presetAttributify,
} from 'unocss';
import presetRemToPx from '@unocss/preset-rem-to-px';

export default defineConfig({
    content: [
        './src/**/*.{vue,js,ts,jsx,tsx}',
        '../templates/**/*.{tpl,html}',
        '../modules/**/*.{tpl,html}',
    ],
    presets: [
        presetWind4(),
        presetAttributify(),
        presetIcons({
            warn: true,
            prefix: ['i-'],
            extraProperties: {
                display: 'inline-block',
                width: '1em',
                height: '1em',
            },
        }),
        presetRemToPx({ baseFontSize: 4 }),
    ],
    shortcuts: [
        ['wh-full', 'w-full h-full'],
        ['f-c-c', 'flex justify-center items-center'],
        ['flex-col', 'flex flex-col'],
        [
            'card-border',
            'border border-solid border-light_border dark:border-dark_border',
        ],
        ['auto-bg', 'bg-white dark:bg-dark'],
        ['auto-bg-hover', 'hover:bg-#eaf0f1 hover:dark:bg-#1b2429'],
        ['auto-bg-highlight', 'bg-#eaf0f1 dark:bg-#1b2429'],
        ['text-highlight', 'rounded-4 px-8 py-2 auto-bg-highlight'],
    ],
    rules: [
        [
            'card-shadow',
            {
                'box-shadow':
                    '0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017',
            },
        ],
    ],
    theme: {
        colors: {
            primary: 'var(--primary-color)',
            primary_hover: 'var(--primary-color-hover)',
            primary_pressed: 'var(--primary-color-pressed)',
            primary_active: 'var(--primary-color-active)',
            info: 'var(--info-color)',
            info_hover: 'var(--info-color-hover)',
            info_pressed: 'var(--info-color-pressed)',
            info_active: 'var(--info-color-active)',
            success: 'var(--success-color)',
            success_hover: 'var(--success-color-hover)',
            success_pressed: 'var(--success-color-pressed)',
            success_active: 'var(--success-color-active)',
            warning: 'var(--warning-color)',
            warning_hover: 'var(--warning-color-hover)',
            warning_pressed: 'var(--warning-color-pressed)',
            warning_active: 'var(--warning-color-active)',
            error: 'var(--error-color)',
            error_hover: 'var(--error-color-hover)',
            error_pressed: 'var(--error-color-pressed)',
            error_active: 'var(--error-color-active)',
            dark: '#18181c',
            light_border: '#efeff5',
            dark_border: '#2d2d30',
        },
    },
});
