import { definePreset } from '@unocss/core';

const remRE = /(-?[.\d]+)rem/g;
const presetRemToPx = definePreset((options = {}) => {
  const {
    baseFontSize = 16
  } = options;
  return {
    name: "@unocss/preset-rem-to-px",
    postprocess: (util) => {
      util.entries.forEach((i) => {
        const value = i[1];
        if (typeof value === "string" && remRE.test(value))
          i[1] = value.replace(remRE, (_, p1) => `${p1 * baseFontSize}px`);
      });
    }
  };
});

export { presetRemToPx as default, presetRemToPx };
