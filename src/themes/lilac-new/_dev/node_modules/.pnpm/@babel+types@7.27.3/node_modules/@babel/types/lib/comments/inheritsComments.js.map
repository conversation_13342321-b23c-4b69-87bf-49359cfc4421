{"version": 3, "names": ["_inheritTrailingComments", "require", "_inheritLeadingComments", "_inheritInnerComments", "inheritsComments", "child", "parent", "inheritTrailingComments", "inheritLeadingComments", "inheritInnerComments"], "sources": ["../../src/comments/inheritsComments.ts"], "sourcesContent": ["import inheritTrailingComments from \"./inheritTrailingComments.ts\";\nimport inheritLeadingComments from \"./inheritLeadingComments.ts\";\nimport inheritInnerComments from \"./inheritInnerComments.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Inherit all unique comments from `parent` node to `child` node.\n */\nexport default function inheritsComments<T extends t.Node>(\n  child: T,\n  parent: t.Node,\n): T {\n  inheritTrailingComments(child, parent);\n  inheritLeadingComments(child, parent);\n  inheritInnerComments(child, parent);\n\n  return child;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,wBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AAMe,SAASG,gBAAgBA,CACtCC,KAAQ,EACRC,MAAc,EACX;EACH,IAAAC,gCAAuB,EAACF,KAAK,EAAEC,MAAM,CAAC;EACtC,IAAAE,+BAAsB,EAACH,KAAK,EAAEC,MAAM,CAAC;EACrC,IAAAG,6BAAoB,EAACJ,KAAK,EAAEC,MAAM,CAAC;EAEnC,OAAOD,KAAK;AACd", "ignoreList": []}