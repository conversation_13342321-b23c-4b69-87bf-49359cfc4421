#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/esm/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules/glob/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/esm/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules/glob/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/glob@10.4.5/node_modules:/data/baora/src/themes/lilac-new/_dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../glob/dist/esm/bin.mjs" "$@"
else
  exec node  "$basedir/../glob/dist/esm/bin.mjs" "$@"
fi
