/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/**/*.{vue,js,ts,jsx,tsx}',
    '../templates/**/*.{tpl,html}',
    '../modules/**/*.{tpl,html}',
    '../assets/**/*.{js,ts}',
    // Explicitly include all template directories
    '../templates/_partials/*.tpl',
    '../templates/catalog/*.tpl',
    '../templates/checkout/*.tpl',
    '../templates/cms/*.tpl',
    '../templates/customer/*.tpl',
    '../templates/errors/*.tpl',
    '../templates/layouts/*.tpl',
    // Include any custom modules
    '../modules/**/views/templates/**/*.tpl'
  ],

  theme: {
    extend: {
      // Custom colors matching the original theme
      colors: {
        'brand-primary': '#007cba',
        'gray-darker': '#363a41',
        'gray-dark': '#5a5a5a',
        'gray': '#6c757d',
        'gray-light': '#f8f9fa',
        'gray-lighter': '#f5f5f5'
      },
      
      // Custom spacing
      spacing: {
        'small-space': '0.625rem',
        'medium-space': '1.25rem',
        'large-space': '2.5rem'
      },
      
      // Custom font families
      fontFamily: {
        'sans': ['Open Sans', 'Helvetica Neue', 'Arial', 'sans-serif']
      },
      
      // Custom breakpoints to match Bootstrap
      screens: {
        'xs': '576px',
        'sm': '768px',
        'md': '992px',
        'lg': '1200px',
        'xl': '1400px'
      },
      
      // Custom shadows
      boxShadow: {
        'theme': '1px 1px 7px 0 rgba(0, 0, 0, 0.15)',
        'inset-theme': 'inset 0 2px 5px 0 rgba(0,0,0,0.11)'
      }
    }
  },
}
