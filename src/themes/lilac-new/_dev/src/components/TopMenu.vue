<template>
  <nav class="top-menu" :class="{ 'is-open': isMobileMenuOpen }">
    <ul class="menu-list" :data-depth="depth">
      <li 
        v-for="item in menuItems" 
        :key="item.id"
        class="menu-item"
        :class="{ 
          'has-submenu': item.children && item.children.length > 0,
          'active': item.current
        }"
        @mouseenter="handleMouseEnter(item)"
        @mouseleave="handleMouseLeave(item)"
      >
        <a 
          :href="item.url"
          :data-depth="item.depth || depth"
          class="menu-link"
          @click="handleMenuClick(item, $event)"
        >
          {{ item.label }}
          <i 
            v-if="item.children && item.children.length > 0" 
            class="material-icons expand-more"
            aria-hidden="true"
          >
            expand_more
          </i>
        </a>
        
        <div 
          v-if="item.children && item.children.length > 0"
          class="sub-menu"
          :class="{ 'show': item.showSubmenu }"
        >
          <TopMenu 
            :menu-items="item.children"
            :depth="(item.depth || depth) + 1"
            @item-clicked="$emit('item-clicked', $event)"
          />
        </div>
      </li>
    </ul>
    
    <!-- Mobile menu toggle -->
    <button 
      v-if="depth === 0"
      class="mobile-menu-toggle md:hidden"
      @click="toggleMobileMenu"
      :aria-expanded="isMobileMenuOpen"
    >
      <i class="material-icons">{{ isMobileMenuOpen ? 'close' : 'menu' }}</i>
    </button>
  </nav>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'

export default {
  name: 'TopMenu',
  props: {
    menuItems: {
      type: Array,
      default: () => []
    },
    depth: {
      type: Number,
      default: 0
    }
  },
  emits: ['item-clicked'],
  setup(props, { emit }) {
    const isMobileMenuOpen = ref(false)
    const processedMenuItems = reactive([])

    const processMenuItems = () => {
      processedMenuItems.splice(0)
      props.menuItems.forEach(item => {
        processedMenuItems.push({
          ...item,
          showSubmenu: false,
          depth: props.depth
        })
      })
    }

    const handleMouseEnter = (item) => {
      if (window.innerWidth >= 768) { // Desktop only
        item.showSubmenu = true
      }
    }

    const handleMouseLeave = (item) => {
      if (window.innerWidth >= 768) { // Desktop only
        item.showSubmenu = false
      }
    }

    const handleMenuClick = (item, event) => {
      // On mobile, toggle submenu instead of navigating
      if (window.innerWidth < 768 && item.children && item.children.length > 0) {
        event.preventDefault()
        item.showSubmenu = !item.showSubmenu
        return
      }

      emit('item-clicked', item)
    }

    const toggleMobileMenu = () => {
      isMobileMenuOpen.value = !isMobileMenuOpen.value
      
      // Toggle body class for mobile menu
      if (isMobileMenuOpen.value) {
        document.body.classList.add('mobile-menu-open')
      } else {
        document.body.classList.remove('mobile-menu-open')
      }
    }

    const handleResize = () => {
      // Close mobile menu on desktop
      if (window.innerWidth >= 768 && isMobileMenuOpen.value) {
        toggleMobileMenu()
      }
    }

    onMounted(() => {
      processMenuItems()
      window.addEventListener('resize', handleResize)
    })

    return {
      isMobileMenuOpen,
      menuItems: processedMenuItems,
      handleMouseEnter,
      handleMouseLeave,
      handleMenuClick,
      toggleMobileMenu
    }
  }
}
</script>

<style scoped>
.top-menu {
  position: relative;
}

.menu-list {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu-item {
  position: relative;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: #363a41;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 0.875rem;
  font-weight: 500;
}

.menu-link:hover {
  color: #007cba;
}

.menu-link[data-depth="0"] {
  color: #6c757d;
  text-transform: uppercase;
}

.menu-item.has-submenu .menu-link {
  padding-right: 2rem;
}

.menu-item.active .menu-link {
  color: #007cba;
}

.sub-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  border: 1px solid #e5e7eb;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transform: translateY(0.5rem);
  transition: all 0.2s ease-out;
  z-index: 50;
  min-width: 200px;
}

.sub-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.material-icons.expand-more {
  position: absolute;
  right: 0.5rem;
  color: #9ca3af;
  font-size: 18px;
}

.mobile-menu-toggle {
  padding: 0.5rem;
  color: #363a41;
}

.mobile-menu-toggle:hover {
  color: #007cba;
}

.mobile-menu-toggle:focus {
  outline: none;
}

/* Mobile styles */
@media (max-width: 767px) {
  .menu-list {
    flex-direction: column;
    width: 100%;
    background-color: white;
    border-top: 1px solid #e5e7eb;
    position: absolute;
    top: 100%;
    left: 0;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-0.5rem);
    transition: all 0.3s ease-out;
  }

  .top-menu.is-open .menu-list {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .menu-item {
    width: 100%;
    border-bottom: 1px solid #f3f4f6;
  }

  .menu-link {
    width: 100%;
    padding: 1rem 1.5rem;
    text-align: left;
  }

  .sub-menu {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #f9fafb;
    border: 0;
    box-shadow: none;
    opacity: 1;
    visibility: visible;
    transform: none;
  }

  .sub-menu .menu-link {
    padding-left: 3rem;
    font-size: 0.875rem;
  }
}

/* Deep selector for nested menus */
:deep(.menu-list[data-depth="1"]) {
  flex-direction: column;
}

:deep(.menu-list[data-depth="1"] .menu-link) {
  font-size: 0.875rem;
  color: #4b5563;
  text-transform: none;
}

:deep(.menu-list[data-depth="1"] .menu-link:hover) {
  color: #007cba;
}
</style>
