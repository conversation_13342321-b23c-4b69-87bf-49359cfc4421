<template>
  <div 
    class="dropdown"
    :class="{ 'show': isOpen }"
    @click.stop
  >
    <button
      class="dropdown-toggle"
      :class="buttonClass"
      type="button"
      @click="toggle"
      :aria-expanded="isOpen"
    >
      <slot name="trigger">
        {{ triggerText }}
        <i class="material-icons expand-more" aria-hidden="true">expand_more</i>
      </slot>
    </button>
    
    <div 
      class="dropdown-menu"
      :class="{ 'show': isOpen }"
      @click.stop
    >
      <slot name="content">
        <div v-for="item in items" :key="item.id" class="dropdown-item">
          <a :href="item.url" @click="handleItemClick(item)">
            {{ item.label }}
          </a>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'DropDown',
  props: {
    triggerText: {
      type: String,
      default: 'Dropdown'
    },
    buttonClass: {
      type: String,
      default: 'btn btn-secondary'
    },
    items: {
      type: Array,
      default: () => []
    },
    autoClose: {
      type: Boolean,
      default: true
    }
  },
  emits: ['item-selected', 'opened', 'closed'],
  setup(props, { emit }) {
    const isOpen = ref(false)

    const toggle = () => {
      if (isOpen.value) {
        close()
      } else {
        open()
      }
    }

    const open = () => {
      isOpen.value = true
      emit('opened')
    }

    const close = () => {
      isOpen.value = false
      emit('closed')
    }

    const handleItemClick = (item) => {
      emit('item-selected', item)
      if (props.autoClose) {
        close()
      }
    }

    const handleClickOutside = (event) => {
      if (isOpen.value && !event.target.closest('.dropdown')) {
        close()
      }
    }

    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && isOpen.value) {
        close()
      }
    }

    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
      document.addEventListener('keydown', handleEscapeKey)
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
      document.removeEventListener('keydown', handleEscapeKey)
    })

    return {
      isOpen,
      toggle,
      open,
      close,
      handleItemClick
    }
  }
}
</script>

<style scoped>
.dropdown {
  @apply relative inline-block;
}

.dropdown-toggle {
  @apply flex items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary;
}

.dropdown-menu {
  @apply absolute z-50 mt-1 bg-white border border-gray-300 rounded-md shadow-lg opacity-0 invisible transform scale-95 transition-all duration-200 ease-out;
  min-width: 100%;
}

.dropdown-menu.show {
  @apply opacity-100 visible scale-100;
}

.dropdown-item {
  @apply block;
}

.dropdown-item a {
  @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 no-underline;
}

.material-icons.expand-more {
  @apply ml-2 text-gray-400;
  font-size: 18px;
}
</style>
