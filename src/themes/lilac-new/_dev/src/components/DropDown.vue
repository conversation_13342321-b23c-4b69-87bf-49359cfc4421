<template>
  <div 
    class="dropdown"
    :class="{ 'show': isOpen }"
    @click.stop
  >
    <button
      class="dropdown-toggle"
      :class="buttonClass"
      type="button"
      @click="toggle"
      :aria-expanded="isOpen"
    >
      <slot name="trigger">
        {{ triggerText }}
        <i class="material-icons expand-more" aria-hidden="true">expand_more</i>
      </slot>
    </button>
    
    <div 
      class="dropdown-menu"
      :class="{ 'show': isOpen }"
      @click.stop
    >
      <slot name="content">
        <div v-for="item in items" :key="item.id" class="dropdown-item">
          <a :href="item.url" @click="handleItemClick(item)">
            {{ item.label }}
          </a>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'DropDown',
  props: {
    triggerText: {
      type: String,
      default: 'Dropdown'
    },
    buttonClass: {
      type: String,
      default: 'btn btn-secondary'
    },
    items: {
      type: Array,
      default: () => []
    },
    autoClose: {
      type: Boolean,
      default: true
    }
  },
  emits: ['item-selected', 'opened', 'closed'],
  setup(props, { emit }) {
    const isOpen = ref(false)

    const toggle = () => {
      if (isOpen.value) {
        close()
      } else {
        open()
      }
    }

    const open = () => {
      isOpen.value = true
      emit('opened')
    }

    const close = () => {
      isOpen.value = false
      emit('closed')
    }

    const handleItemClick = (item) => {
      emit('item-selected', item)
      if (props.autoClose) {
        close()
      }
    }

    const handleClickOutside = (event) => {
      if (isOpen.value && !event.target.closest('.dropdown')) {
        close()
      }
    }

    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && isOpen.value) {
        close()
      }
    }

    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
      document.addEventListener('keydown', handleEscapeKey)
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
      document.removeEventListener('keydown', handleEscapeKey)
    })

    return {
      isOpen,
      toggle,
      open,
      close,
      handleItemClick
    }
  }
}
</script>

<style scoped>
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.dropdown-toggle:hover {
  background-color: #f9fafb;
}

.dropdown-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.dropdown-menu {
  position: absolute;
  z-index: 50;
  margin-top: 0.25rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transform: scale(0.95);
  transition: all 0.2s ease-out;
  min-width: 100%;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.dropdown-item {
  display: block;
}

.dropdown-item a {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #374151;
  text-decoration: none;
}

.dropdown-item a:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.material-icons.expand-more {
  margin-left: 0.5rem;
  color: #9ca3af;
  font-size: 18px;
}
</style>
