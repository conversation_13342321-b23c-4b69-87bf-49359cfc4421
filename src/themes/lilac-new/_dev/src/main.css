@tailwind base;
@tailwind components;
@tailwind utilities;

/*
 * Template Classes Reference
 * This comment helps Tailwind's JIT compiler detect classes used in .tpl files
 *
 * Common classes used in templates:
 * h-[40px] w-full max-w-full container row col col-12 col-md-6 col-lg-4 col-xl-3
 * flex flex-row flex-col flex-wrap justify-center justify-between items-center
 * p-0 p-1 p-2 p-3 p-4 p-5 p-6 m-0 m-1 m-2 m-3 m-4 m-5 m-6 px-2 px-3 px-4 py-2 py-3 py-4
 * text-left text-center text-right text-sm text-base text-lg text-xl font-normal font-medium font-semibold font-bold
 * bg-white bg-gray-50 bg-gray-100 bg-gray-200 bg-brand-primary text-white text-black text-gray-500 text-gray-600 text-gray-700 text-gray-800 text-gray-900 text-brand-primary
 * border border-0 border-t border-b border-gray-200 border-gray-300 rounded rounded-md rounded-lg
 * btn btn-primary btn-secondary btn-sm btn-lg form-control form-group input-group
 * block inline-block flex inline-flex grid hidden
 * sm:block sm:hidden md:block md:hidden lg:block lg:hidden xl:block xl:hidden
 * hover:bg-gray-100 hover:text-brand-primary focus:outline-none focus:ring-2 focus:ring-brand-primary
 * transition-colors duration-200 ease-in-out
 * shadow shadow-sm shadow-md shadow-lg opacity-50 opacity-75 opacity-100
 * absolute relative fixed top-0 right-0 bottom-0 left-0 z-10 z-20 z-30 z-40 z-50
 * overflow-hidden overflow-auto max-h-96 min-h-0
 * sr-only not-sr-only
 */

/* Ensure commonly used classes are always available */
/* These classes are referenced here to ensure Tailwind includes them */

/* Custom component styles */
@layer components {
  .btn {
    @apply px-4 py-2 font-medium rounded transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-brand-primary text-white hover:bg-blue-700;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }

  .form-control {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-brand-primary;
  }

  .form-group {
    @apply mb-4;
  }

  .input-group {
    @apply flex;
  }
}

