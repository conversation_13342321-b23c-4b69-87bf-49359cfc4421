/**
 * 2007-2019 PrestaShop and Contributors
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.prestashop.com for more information.
 *
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2007-2019 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 * International Registered Trademark & Property of PrestaShop SA
 */

import { createApp } from 'vue'
import './main.css'

// Development helpers (only in dev mode)
if (import.meta.env.DEV) {
  import('../dev-helper.js')
}

// Import Vue components
import DropDown from './components/DropDown.vue'
import TopMenu from './components/TopMenu.vue'
import ProductMiniature from './components/ProductMiniature.vue'
import ProductSelect from './components/ProductSelect.vue'
import BlockCart from './components/BlockCart.vue'
import FormHandler from './components/FormHandler.vue'
import CartQuantity from './components/CartQuantity.vue'
import PromoCode from './components/PromoCode.vue'
import PasswordToggle from './components/PasswordToggle.vue'
import SearchWidget from './components/SearchWidget.vue'
import Modal from './components/Modal.vue'

// Import page-specific modules
import './modules/responsive'
import './modules/checkout'
import './modules/customer'
import './modules/listing'
import './modules/product'
import './modules/cart'

// Import utilities
import { prestashopEventBus } from './utils/prestashop'
import $ from './utils/jquery-replacement'

// Global Vue app instance
let app = null

// Initialize Vue components when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Create Vue app
  app = createApp({
    components: {
      DropDown,
      TopMenu,
      ProductMiniature,
      ProductSelect,
      BlockCart,
      FormHandler,
      CartQuantity,
      PromoCode,
      PasswordToggle,
      SearchWidget,
      Modal
    }
  })

  // Mount Vue components to existing elements
  initializeComponents()
  
  // Initialize legacy functionality
  initializeLegacyFeatures()
})

function initializeComponents() {
  // Initialize dropdown components
  const dropdownElements = document.querySelectorAll('.js-dropdown')
  dropdownElements.forEach(el => {
    if (!el.__vue__) {
      const dropdownApp = createApp(DropDown)
      dropdownApp.mount(el)
    }
  })

  // Initialize top menu
  const topMenuElements = document.querySelectorAll('.js-top-menu ul[data-depth="0"]')
  topMenuElements.forEach(el => {
    if (!el.__vue__) {
      const topMenuApp = createApp(TopMenu)
      topMenuApp.mount(el)
    }
  })

  // Initialize product miniatures
  const productMiniatureElements = document.querySelectorAll('.js-product-miniature')
  productMiniatureElements.forEach(el => {
    if (!el.__vue__) {
      const productApp = createApp(ProductMiniature)
      productApp.mount(el)
    }
  })

  // Initialize product selects
  const productSelectElements = document.querySelectorAll('.js-product-select')
  productSelectElements.forEach(el => {
    if (!el.__vue__) {
      const selectApp = createApp(ProductSelect)
      selectApp.mount(el)
    }
  })

  // Initialize block cart
  const blockCartElements = document.querySelectorAll('.js-block-cart')
  blockCartElements.forEach(el => {
    if (!el.__vue__) {
      const cartApp = createApp(BlockCart)
      cartApp.mount(el)
    }
  })

  // Initialize forms
  const formElements = document.querySelectorAll('.js-form')
  formElements.forEach(el => {
    if (!el.__vue__) {
      const formApp = createApp(FormHandler)
      formApp.mount(el)
    }
  })

  // Initialize cart quantity controls
  const quantityElements = document.querySelectorAll('.js-cart-line-product-quantity')
  quantityElements.forEach(el => {
    if (!el.__vue__ && el.closest('.quantity-selector')) {
      const quantityApp = createApp(CartQuantity)
      quantityApp.mount(el.closest('.quantity-selector'))
    }
  })

  // Initialize promo code sections
  const promoElements = document.querySelectorAll('.js-promo-code')
  promoElements.forEach(el => {
    if (!el.__vue__) {
      const promoApp = createApp(PromoCode)
      promoApp.mount(el)
    }
  })

  // Initialize password toggle fields
  const passwordElements = document.querySelectorAll('.js-visible-password')
  passwordElements.forEach(el => {
    if (!el.__vue__ && el.closest('.password-toggle-container')) {
      const passwordApp = createApp(PasswordToggle)
      passwordApp.mount(el.closest('.password-toggle-container'))
    }
  })

  // Initialize search widgets
  const searchElements = document.querySelectorAll('.js-search-widget')
  searchElements.forEach(el => {
    if (!el.__vue__) {
      const searchApp = createApp(SearchWidget)
      searchApp.mount(el)
    }
  })

  // Initialize modals
  const modalElements = document.querySelectorAll('.js-modal')
  modalElements.forEach(el => {
    if (!el.__vue__) {
      const modalApp = createApp(Modal)
      modalApp.mount(el)
    }
  })
}

function initializeLegacyFeatures() {
  // Maintain compatibility with PrestaShop's global prestashop object
  if (typeof window.prestashop !== 'undefined') {
    // Extend prestashop object with event bus functionality
    Object.assign(window.prestashop, prestashopEventBus)
  }
}

// Export for global access
window.ThemeApp = {
  app,
  initializeComponents,
  createApp
}
