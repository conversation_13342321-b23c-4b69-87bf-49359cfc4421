/**
 * Product listing functionality
 * Handles category pages, search results, filters, and sorting
 */

import { prestashopEventBus, PRESTASHOP_EVENTS, prestashopUtils } from '../utils/prestashop'

class ListingHandler {
  constructor() {
    this.currentFilters = {}
    this.currentSort = null
    this.currentPage = 1
    this.isLoading = false
    this.init()
  }

  init() {
    this.initSorting()
    this.initFilters()
    this.initPagination()
    this.initViewModes()
    this.initQuickView()
    this.setupEventListeners()
  }

  setupEventListeners() {
    // Listen for filter updates
    prestashopEventBus.on('filtersUpdated', (data) => {
      this.handleFiltersUpdate(data)
    })

    // Listen for product list updates
    prestashopEventBus.on('productListUpdated', (data) => {
      this.handleProductListUpdate(data)
    })
  }

  initSorting() {
    const sortSelect = document.querySelector('.products-sort-order select')
    
    if (sortSelect) {
      sortSelect.addEventListener('change', (event) => {
        this.handleSortChange(event.target.value)
      })
    }

    // Sort direction buttons
    const sortButtons = document.querySelectorAll('.sort-by-row .btn')
    sortButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault()
        this.handleSortDirectionChange(button)
      })
    })
  }

  async handleSortChange(sortValue) {
    if (this.isLoading) return

    this.currentSort = sortValue
    await this.updateProductList()
  }

  handleSortDirectionChange(button) {
    const sortField = button.dataset.sortField
    const currentDirection = button.dataset.sortDirection || 'asc'
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc'
    
    button.dataset.sortDirection = newDirection
    
    // Update button appearance
    const icon = button.querySelector('i')
    if (icon) {
      icon.className = newDirection === 'asc' ? 'material-icons expand_less' : 'material-icons expand_more'
    }

    this.currentSort = `${sortField}:${newDirection}`
    this.updateProductList()
  }

  initFilters() {
    // Faceted search filters
    const filterCheckboxes = document.querySelectorAll('#search_filters input[type="checkbox"]')
    const filterRadios = document.querySelectorAll('#search_filters input[type="radio"]')
    const priceSlider = document.querySelector('#price-slider')

    filterCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.handleFilterChange()
      })
    })

    filterRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        this.handleFilterChange()
      })
    })

    if (priceSlider) {
      this.initPriceSlider(priceSlider)
    }

    // Clear filters button
    const clearFiltersBtn = document.querySelector('.js-search-filters-clear-all')
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', (event) => {
        event.preventDefault()
        this.clearAllFilters()
      })
    }

    // Filter collapse/expand
    const filterToggles = document.querySelectorAll('.facet-label')
    filterToggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        this.toggleFilterSection(toggle)
      })
    })
  }

  handleFilterChange() {
    // Debounce filter changes
    clearTimeout(this.filterTimeout)
    this.filterTimeout = setTimeout(() => {
      this.collectFilters()
      this.updateProductList()
    }, 300)
  }

  collectFilters() {
    this.currentFilters = {}

    // Collect checkbox filters
    const checkedBoxes = document.querySelectorAll('#search_filters input[type="checkbox"]:checked')
    checkedBoxes.forEach(checkbox => {
      const filterName = checkbox.name
      if (!this.currentFilters[filterName]) {
        this.currentFilters[filterName] = []
      }
      this.currentFilters[filterName].push(checkbox.value)
    })

    // Collect radio filters
    const checkedRadios = document.querySelectorAll('#search_filters input[type="radio"]:checked')
    checkedRadios.forEach(radio => {
      this.currentFilters[radio.name] = radio.value
    })

    // Collect price range
    const priceMin = document.querySelector('#price-min')
    const priceMax = document.querySelector('#price-max')
    if (priceMin && priceMax) {
      this.currentFilters.price = {
        min: priceMin.value,
        max: priceMax.value
      }
    }
  }

  initPriceSlider(slider) {
    const minInput = document.querySelector('#price-min')
    const maxInput = document.querySelector('#price-max')
    const minHandle = slider.querySelector('.price-slider-min')
    const maxHandle = slider.querySelector('.price-slider-max')

    if (minInput && maxInput && minHandle && maxHandle) {
      this.setupPriceSliderEvents(slider, minInput, maxInput, minHandle, maxHandle)
    }
  }

  setupPriceSliderEvents(slider, minInput, maxInput, minHandle, maxHandle) {
    let isDragging = false
    let activeHandle = null

    const updateSlider = () => {
      const min = parseFloat(minInput.min)
      const max = parseFloat(minInput.max)
      const minVal = parseFloat(minInput.value)
      const maxVal = parseFloat(maxInput.value)

      const minPercent = ((minVal - min) / (max - min)) * 100
      const maxPercent = ((maxVal - min) / (max - min)) * 100

      minHandle.style.left = `${minPercent}%`
      maxHandle.style.left = `${maxPercent}%`

      const track = slider.querySelector('.price-slider-track')
      if (track) {
        track.style.left = `${minPercent}%`
        track.style.width = `${maxPercent - minPercent}%`
      }
    }

    minInput.addEventListener('input', updateSlider)
    maxInput.addEventListener('input', updateSlider)

    // Initialize slider
    updateSlider()
  }

  clearAllFilters() {
    // Clear all checkboxes
    const checkboxes = document.querySelectorAll('#search_filters input[type="checkbox"]')
    checkboxes.forEach(checkbox => {
      checkbox.checked = false
    })

    // Clear all radios
    const radios = document.querySelectorAll('#search_filters input[type="radio"]')
    radios.forEach(radio => {
      radio.checked = false
    })

    // Reset price range
    const priceMin = document.querySelector('#price-min')
    const priceMax = document.querySelector('#price-max')
    if (priceMin && priceMax) {
      priceMin.value = priceMin.min
      priceMax.value = priceMax.max
    }

    this.currentFilters = {}
    this.updateProductList()
  }

  toggleFilterSection(toggle) {
    const section = toggle.closest('.facet')
    const content = section.querySelector('.facet-content')
    
    if (content) {
      const isExpanded = content.style.display !== 'none'
      content.style.display = isExpanded ? 'none' : 'block'
      
      const icon = toggle.querySelector('i')
      if (icon) {
        icon.textContent = isExpanded ? 'expand_more' : 'expand_less'
      }
    }
  }

  initPagination() {
    const paginationLinks = document.querySelectorAll('.pagination a')
    
    paginationLinks.forEach(link => {
      link.addEventListener('click', (event) => {
        event.preventDefault()
        const page = this.extractPageFromUrl(link.href)
        if (page) {
          this.goToPage(page)
        }
      })
    })
  }

  extractPageFromUrl(url) {
    const urlParams = new URLSearchParams(new URL(url).search)
    return urlParams.get('page') || 1
  }

  async goToPage(page) {
    if (this.isLoading) return

    this.currentPage = page
    await this.updateProductList()
  }

  initViewModes() {
    const viewModeButtons = document.querySelectorAll('.display-selector a')
    
    viewModeButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault()
        this.changeViewMode(button)
      })
    })
  }

  changeViewMode(button) {
    const viewMode = button.dataset.viewMode
    const productList = document.querySelector('#products')
    
    if (productList) {
      // Remove existing view mode classes
      productList.classList.remove('grid-view', 'list-view')
      
      // Add new view mode class
      productList.classList.add(`${viewMode}-view`)
      
      // Update active button
      document.querySelectorAll('.display-selector a').forEach(btn => {
        btn.classList.remove('active')
      })
      button.classList.add('active')
      
      // Store preference
      localStorage.setItem('product-view-mode', viewMode)
    }
  }

  initQuickView() {
    document.addEventListener('click', (event) => {
      if (event.target.matches('.quick-view, .js-quick-view')) {
        event.preventDefault()
        this.openQuickView(event.target)
      }
    })
  }

  async openQuickView(button) {
    const productId = button.dataset.idProduct
    const productUrl = button.dataset.productUrl

    if (!productId && !productUrl) return

    try {
      const url = productUrl || `/product/${productId}`
      const response = await fetch(`${url}?quickview=1`, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      const html = await response.text()
      this.showQuickViewModal(html)
    } catch (error) {
      console.error('Quick view error:', error)
    }
  }

  showQuickViewModal(content) {
    // Create modal
    const modal = document.createElement('div')
    modal.className = 'quickview-modal fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex items-center justify-center z-50'
    modal.innerHTML = `
      <div class="quickview-content bg-white rounded-lg max-w-4xl max-h-full overflow-auto m-4 relative">
        <button class="quickview-close absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
        <div class="quickview-body p-6">
          ${content}
        </div>
      </div>
    `

    // Add event listeners
    modal.addEventListener('click', (event) => {
      if (event.target === modal || event.target.matches('.quickview-close')) {
        modal.remove()
      }
    })

    document.addEventListener('keydown', function escapeHandler(event) {
      if (event.key === 'Escape') {
        modal.remove()
        document.removeEventListener('keydown', escapeHandler)
      }
    })

    document.body.appendChild(modal)

    // Initialize components in quick view
    this.initQuickViewComponents(modal)
  }

  initQuickViewComponents(modal) {
    // Reinitialize product components for quick view
    const productImages = modal.querySelectorAll('.product-images img')
    productImages.forEach(img => {
      img.addEventListener('click', () => {
        this.switchQuickViewImage(img)
      })
    })

    // Initialize add to cart in quick view
    const addToCartBtn = modal.querySelector('.add-to-cart')
    if (addToCartBtn) {
      addToCartBtn.addEventListener('click', (event) => {
        event.preventDefault()
        this.handleQuickViewAddToCart(modal)
      })
    }
  }

  switchQuickViewImage(thumbnail) {
    const mainImage = thumbnail.closest('.quickview-content').querySelector('.product-cover img')
    if (mainImage) {
      mainImage.src = thumbnail.dataset.fullSizeUrl || thumbnail.src
    }
  }

  async handleQuickViewAddToCart(modal) {
    const form = modal.querySelector('#add-to-cart-or-refresh')
    if (!form) return

    const formData = new FormData(form)

    try {
      const response = await prestashopUtils.makeRequest(form.action, {
        method: 'POST',
        body: formData
      })

      if (response.success) {
        prestashopEventBus.emit(PRESTASHOP_EVENTS.CART_PRODUCT_ADDED, response)
        modal.remove()
      } else {
        this.showError(response.message || 'Failed to add product to cart')
      }
    } catch (error) {
      console.error('Quick view add to cart error:', error)
      this.showError('Failed to add product to cart')
    }
  }

  async updateProductList() {
    if (this.isLoading) return

    this.isLoading = true
    this.showLoadingState()

    try {
      const params = new URLSearchParams()
      
      // Add filters
      Object.entries(this.currentFilters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`${key}[]`, v))
        } else if (typeof value === 'object') {
          Object.entries(value).forEach(([k, v]) => {
            params.append(`${key}[${k}]`, v)
          })
        } else {
          params.append(key, value)
        }
      })

      // Add sort
      if (this.currentSort) {
        params.append('order', this.currentSort)
      }

      // Add page
      if (this.currentPage > 1) {
        params.append('page', this.currentPage)
      }

      params.append('ajax', '1')

      const response = await fetch(`${window.location.pathname}?${params.toString()}`, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      const data = await response.json()

      if (data.success) {
        this.updateProductListDisplay(data)
        prestashopEventBus.emit('productListUpdated', data)
      }
    } catch (error) {
      console.error('Error updating product list:', error)
    } finally {
      this.isLoading = false
      this.hideLoadingState()
    }
  }

  updateProductListDisplay(data) {
    // Update products
    if (data.products_html) {
      const productsContainer = document.querySelector('#products')
      if (productsContainer) {
        productsContainer.innerHTML = data.products_html
      }
    }

    // Update pagination
    if (data.pagination_html) {
      const paginationContainer = document.querySelector('.pagination-container')
      if (paginationContainer) {
        paginationContainer.innerHTML = data.pagination_html
        this.initPagination() // Reinitialize pagination events
      }
    }

    // Update filters
    if (data.filters_html) {
      const filtersContainer = document.querySelector('#search_filters')
      if (filtersContainer) {
        filtersContainer.innerHTML = data.filters_html
        this.initFilters() // Reinitialize filter events
      }
    }

    // Update product count
    if (data.total_products !== undefined) {
      const countElements = document.querySelectorAll('.total-products')
      countElements.forEach(element => {
        element.textContent = data.total_products
      })
    }
  }

  showLoadingState() {
    const productsContainer = document.querySelector('#products')
    if (productsContainer) {
      productsContainer.classList.add('loading')
      
      // Add loading overlay
      const overlay = document.createElement('div')
      overlay.className = 'loading-overlay absolute top-0 left-0 right-0 bottom-0 bg-white bg-opacity-75 flex items-center justify-center'
      overlay.innerHTML = '<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>'
      
      productsContainer.style.position = 'relative'
      productsContainer.appendChild(overlay)
    }
  }

  hideLoadingState() {
    const productsContainer = document.querySelector('#products')
    if (productsContainer) {
      productsContainer.classList.remove('loading')
      
      const overlay = productsContainer.querySelector('.loading-overlay')
      if (overlay) {
        overlay.remove()
      }
    }
  }

  showError(message) {
    prestashopEventBus.emit('showNotification', { message, type: 'error' })
  }

  handleFiltersUpdate(data) {
    // Handle external filter updates
    this.currentFilters = { ...this.currentFilters, ...data.filters }
    this.updateProductList()
  }

  handleProductListUpdate(data) {
    // Handle product list updates from other sources
    console.log('Product list updated:', data)
  }
}

// Initialize listing handler
const listingHandler = new ListingHandler()

export default listingHandler
