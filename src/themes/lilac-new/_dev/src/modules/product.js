/**
 * Product page functionality
 * Handles product images, variants, quantity, and add to cart
 */

import { prestashopEventBus, PRESTASHOP_EVENTS, prestashopUtils } from '../utils/prestashop'

class ProductHandler {
  constructor() {
    this.currentProductId = null
    this.selectedCombination = null
    this.init()
  }

  init() {
    this.initProductImages()
    this.initQuantityControls()
    this.initAddToCart()
    this.initProductTabs()
    this.initProductFlags()
    this.setupEventListeners()
  }

  setupEventListeners() {
    // Listen for product updates
    prestashopEventBus.on(PRESTASHOP_EVENTS.PRODUCT_UPDATED, (data) => {
      this.handleProductUpdate(data)
    })

    // Listen for variant changes
    document.addEventListener('change', (event) => {
      if (event.target.matches('[name^="group_"]')) {
        this.handleVariantChange()
      }
    })
  }

  initProductImages() {
    // Main product image gallery
    const mainImage = document.querySelector('.js-qv-product-cover, .product-cover img')
    const thumbnails = document.querySelectorAll('.js-thumb, .product-images .thumbnail')

    if (mainImage && thumbnails.length) {
      thumbnails.forEach(thumb => {
        thumb.addEventListener('click', (event) => {
          event.preventDefault()
          this.switchMainImage(thumb, mainImage)
        })
      })
    }

    // Image zoom functionality
    this.initImageZoom()

    // Image modal/lightbox
    this.initImageModal()
  }

  switchMainImage(thumbnail, mainImage) {
    const newImageSrc = thumbnail.dataset.imageUrl || thumbnail.href
    const newImageSrcset = thumbnail.dataset.imageSrcset
    
    if (newImageSrc) {
      mainImage.src = newImageSrc
      if (newImageSrcset) {
        mainImage.srcset = newImageSrcset
      }
      
      // Update active thumbnail
      document.querySelectorAll('.js-thumb, .product-images .thumbnail').forEach(t => {
        t.classList.remove('selected', 'active')
      })
      thumbnail.classList.add('selected', 'active')
    }
  }

  initImageZoom() {
    const zoomContainer = document.querySelector('.product-cover')
    const zoomImage = zoomContainer?.querySelector('img')

    if (zoomContainer && zoomImage) {
      zoomContainer.addEventListener('mousemove', (event) => {
        this.handleImageZoom(event, zoomContainer, zoomImage)
      })

      zoomContainer.addEventListener('mouseleave', () => {
        this.hideImageZoom()
      })
    }
  }

  handleImageZoom(event, container, image) {
    const rect = container.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 100
    const y = ((event.clientY - rect.top) / rect.height) * 100

    image.style.transformOrigin = `${x}% ${y}%`
    image.style.transform = 'scale(2)'
  }

  hideImageZoom() {
    const zoomImage = document.querySelector('.product-cover img')
    if (zoomImage) {
      zoomImage.style.transform = 'scale(1)'
      zoomImage.style.transformOrigin = 'center'
    }
  }

  initImageModal() {
    const productImages = document.querySelectorAll('.product-cover img, .js-qv-product-cover')
    
    productImages.forEach(image => {
      image.addEventListener('click', (event) => {
        if (event.target.dataset.zoomUrl) {
          this.openImageModal(event.target.dataset.zoomUrl)
        }
      })
    })
  }

  openImageModal(imageUrl) {
    // Create modal overlay
    const modal = document.createElement('div')
    modal.className = 'image-modal fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-75 flex items-center justify-center z-50'
    modal.innerHTML = `
      <div class="relative max-w-4xl max-h-full p-4">
        <img src="${imageUrl}" alt="Product image" class="max-w-full max-h-full object-contain">
        <button class="absolute top-2 right-2 text-white text-2xl hover:text-gray-300">&times;</button>
      </div>
    `

    // Add event listeners
    modal.addEventListener('click', (event) => {
      if (event.target === modal || event.target.matches('button')) {
        modal.remove()
      }
    })

    document.addEventListener('keydown', function escapeHandler(event) {
      if (event.key === 'Escape') {
        modal.remove()
        document.removeEventListener('keydown', escapeHandler)
      }
    })

    document.body.appendChild(modal)
  }

  initQuantityControls() {
    const quantityInput = document.querySelector('#quantity_wanted')
    const increaseBtn = document.querySelector('.js-increase-product-quantity')
    const decreaseBtn = document.querySelector('.js-decrease-product-quantity')

    if (quantityInput) {
      quantityInput.addEventListener('change', () => {
        this.validateQuantity(quantityInput)
      })
    }

    if (increaseBtn) {
      increaseBtn.addEventListener('click', (event) => {
        event.preventDefault()
        this.increaseQuantity()
      })
    }

    if (decreaseBtn) {
      decreaseBtn.addEventListener('click', (event) => {
        event.preventDefault()
        this.decreaseQuantity()
      })
    }
  }

  validateQuantity(input) {
    const min = parseInt(input.min) || 1
    const max = parseInt(input.max) || Infinity
    let value = parseInt(input.value) || min

    if (value < min) value = min
    if (value > max) value = max

    input.value = value
  }

  increaseQuantity() {
    const input = document.querySelector('#quantity_wanted')
    if (input) {
      const max = parseInt(input.max) || Infinity
      const current = parseInt(input.value) || 1
      if (current < max) {
        input.value = current + 1
      }
    }
  }

  decreaseQuantity() {
    const input = document.querySelector('#quantity_wanted')
    if (input) {
      const min = parseInt(input.min) || 1
      const current = parseInt(input.value) || 1
      if (current > min) {
        input.value = current - 1
      }
    }
  }

  initAddToCart() {
    const addToCartBtn = document.querySelector('.add-to-cart, .js-add-to-cart')
    
    if (addToCartBtn) {
      addToCartBtn.addEventListener('click', (event) => {
        event.preventDefault()
        this.addToCart()
      })
    }
  }

  async addToCart() {
    const form = document.querySelector('#add-to-cart-or-refresh')
    if (!form) return

    const formData = new FormData(form)
    const addToCartBtn = document.querySelector('.add-to-cart, .js-add-to-cart')

    // Disable button during request
    if (addToCartBtn) {
      addToCartBtn.disabled = true
      addToCartBtn.textContent = 'Adding...'
    }

    try {
      const response = await prestashopUtils.makeRequest(form.action, {
        method: 'POST',
        body: formData
      })

      if (response.success) {
        prestashopEventBus.emit(PRESTASHOP_EVENTS.CART_PRODUCT_ADDED, {
          product: response.product,
          cart: response.cart
        })
        
        // Show success message
        this.showNotification('Product added to cart successfully', 'success')
      } else {
        this.showNotification(response.message || 'Failed to add product to cart', 'error')
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      this.showNotification('Failed to add product to cart', 'error')
    } finally {
      // Re-enable button
      if (addToCartBtn) {
        addToCartBtn.disabled = false
        addToCartBtn.textContent = 'Add to cart'
      }
    }
  }

  handleVariantChange() {
    const form = document.querySelector('#add-to-cart-or-refresh')
    if (!form) return

    const formData = new FormData(form)
    
    // Find selected combination
    this.updateProductInfo(formData)
  }

  async updateProductInfo(formData) {
    try {
      formData.append('ajax', '1')
      formData.append('action', 'refresh')

      const response = await prestashopUtils.makeRequest(window.location.href, {
        method: 'POST',
        body: formData
      })

      if (response.success) {
        this.updateProductDisplay(response)
        prestashopEventBus.emit(PRESTASHOP_EVENTS.PRODUCT_UPDATED, response)
      }
    } catch (error) {
      console.error('Error updating product info:', error)
    }
  }

  updateProductDisplay(data) {
    // Update price
    if (data.product_price) {
      const priceElements = document.querySelectorAll('.current-price, .product-price')
      priceElements.forEach(el => {
        el.textContent = data.product_price
      })
    }

    // Update availability
    if (data.product_availability) {
      const availabilityElement = document.querySelector('#product-availability')
      if (availabilityElement) {
        availabilityElement.innerHTML = data.product_availability
      }
    }

    // Update product images
    if (data.product_images) {
      this.updateProductImages(data.product_images)
    }

    // Update add to cart button
    const addToCartBtn = document.querySelector('.add-to-cart, .js-add-to-cart')
    if (addToCartBtn) {
      addToCartBtn.disabled = !data.product_available
    }
  }

  updateProductImages(images) {
    const mainImage = document.querySelector('.js-qv-product-cover, .product-cover img')
    const thumbnailContainer = document.querySelector('.product-images, .js-thumbnails')

    if (mainImage && images.length > 0) {
      mainImage.src = images[0].large.url
      mainImage.srcset = images[0].large.srcset || ''
    }

    if (thumbnailContainer) {
      // Update thumbnails
      const thumbnails = thumbnailContainer.querySelectorAll('.thumbnail')
      thumbnails.forEach((thumb, index) => {
        if (images[index]) {
          const img = thumb.querySelector('img')
          if (img) {
            img.src = images[index].small.url
            img.srcset = images[index].small.srcset || ''
          }
          thumb.dataset.imageUrl = images[index].large.url
        }
      })
    }
  }

  initProductTabs() {
    const tabButtons = document.querySelectorAll('.nav-tabs .nav-link')
    const tabPanes = document.querySelectorAll('.tab-pane')

    tabButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault()
        this.switchTab(button, tabButtons, tabPanes)
      })
    })
  }

  switchTab(activeButton, allButtons, allPanes) {
    const targetId = activeButton.getAttribute('href').substring(1)

    // Update buttons
    allButtons.forEach(btn => btn.classList.remove('active'))
    activeButton.classList.add('active')

    // Update panes
    allPanes.forEach(pane => {
      pane.classList.remove('active', 'show')
      if (pane.id === targetId) {
        pane.classList.add('active', 'show')
      }
    })
  }

  initProductFlags() {
    // Handle product flags (new, sale, etc.)
    const flags = document.querySelectorAll('.product-flag')
    flags.forEach(flag => {
      // Add any flag-specific functionality here
      flag.classList.add('animate-pulse')
      setTimeout(() => {
        flag.classList.remove('animate-pulse')
      }, 2000)
    })
  }

  showNotification(message, type) {
    prestashopEventBus.emit('showNotification', { message, type })
  }
}

// Initialize product handler
const productHandler = new ProductHandler()

export default productHandler
