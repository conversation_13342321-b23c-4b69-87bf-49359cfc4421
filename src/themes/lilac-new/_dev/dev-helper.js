/**
 * Development Helper Script
 * Provides utilities for development mode testing
 */

// Development mode indicator
if (import.meta.env.DEV) {
  console.log('🚀 Lilac New Theme - Development Mode Active')
  console.log('📡 Vite HMR enabled - changes will be reflected instantly')
  console.log('🎨 Tailwind CSS with JIT compilation')
  console.log('⚡ Vue 3 with hot component reloading')
  
  // Add development indicator to page
  const devIndicator = document.createElement('div')
  devIndicator.innerHTML = `
    <div style="
      position: fixed;
      top: 10px;
      right: 10px;
      background: #10b981;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: bold;
      z-index: 9999;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      font-family: monospace;
    ">
      🚀 DEV MODE
    </div>
  `
  document.body.appendChild(devIndicator)
  
  // Log component mounting
  const originalCreateApp = window.ThemeApp?.createApp
  if (originalCreateApp) {
    window.ThemeApp.createApp = function(...args) {
      console.log('🔧 Vue component mounted:', args[0]?.name || 'Anonymous')
      return originalCreateApp.apply(this, args)
    }
  }
  
  // CSS hot reload notification
  if (import.meta.hot) {
    import.meta.hot.on('css-update', () => {
      console.log('🎨 CSS updated via HMR')
    })
    
    import.meta.hot.on('vue-reload', () => {
      console.log('⚡ Vue component reloaded')
    })
  }
  
  // Development shortcuts
  window.devHelpers = {
    // Toggle Tailwind debug mode
    toggleTailwindDebug() {
      document.documentElement.classList.toggle('debug-screens')
      console.log('🔍 Tailwind debug mode toggled')
    },
    
    // Show all Vue components
    showVueComponents() {
      const components = document.querySelectorAll('[data-v-]')
      console.log(`📦 Found ${components.length} Vue components:`, components)
      return components
    },
    
    // Test responsive breakpoints
    testBreakpoints() {
      const breakpoints = {
        'xs': '576px',
        'sm': '768px', 
        'md': '992px',
        'lg': '1200px',
        'xl': '1400px'
      }
      
      console.log('📱 Responsive breakpoints:', breakpoints)
      console.log('Current width:', window.innerWidth + 'px')
      
      Object.entries(breakpoints).forEach(([name, width]) => {
        const px = parseInt(width)
        if (window.innerWidth >= px) {
          console.log(`✅ ${name}: ${width} (active)`)
        } else {
          console.log(`❌ ${name}: ${width}`)
        }
      })
    },
    
    // Reload all Vue components
    reloadComponents() {
      if (window.ThemeApp?.initializeComponents) {
        window.ThemeApp.initializeComponents()
        console.log('🔄 Vue components reloaded')
      }
    },
    
    // Test PrestaShop integration
    testPrestashop() {
      console.log('🛒 PrestaShop integration test:')
      console.log('- prestashop object:', typeof window.prestashop)
      console.log('- static_token:', typeof window.static_token)
      console.log('- urls object:', typeof window.urls)
      
      if (window.prestashop) {
        console.log('- customer:', window.prestashop.customer)
        console.log('- cart:', window.prestashop.cart)
        console.log('- page:', window.prestashop.page)
      }
    }
  }
  
  // Add development styles for debugging
  const devStyles = document.createElement('style')
  devStyles.textContent = `
    /* Development mode styles */
    .debug-screens::before {
      content: 'XS';
      position: fixed;
      top: 50px;
      right: 10px;
      background: #ef4444;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: bold;
      z-index: 9999;
    }
    
    @media (min-width: 576px) {
      .debug-screens::before { content: 'SM'; background: #f59e0b; }
    }
    
    @media (min-width: 768px) {
      .debug-screens::before { content: 'MD'; background: #10b981; }
    }
    
    @media (min-width: 992px) {
      .debug-screens::before { content: 'LG'; background: #3b82f6; }
    }
    
    @media (min-width: 1200px) {
      .debug-screens::before { content: 'XL'; background: #8b5cf6; }
    }
    
    /* Highlight Vue components in dev mode */
    [data-v-] {
      outline: 1px dashed rgba(16, 185, 129, 0.3) !important;
    }
    
    [data-v-]:hover {
      outline: 2px solid rgba(16, 185, 129, 0.6) !important;
    }
  `
  document.head.appendChild(devStyles)
  
  // Development console commands help
  console.log(`
🛠️  Development Helper Commands:
- devHelpers.toggleTailwindDebug() - Show responsive breakpoints
- devHelpers.showVueComponents() - List all Vue components  
- devHelpers.testBreakpoints() - Test responsive breakpoints
- devHelpers.reloadComponents() - Reload Vue components
- devHelpers.testPrestashop() - Test PrestaShop integration

💡 Tips:
- Install Vue DevTools browser extension for better debugging
- Use browser dev tools to inspect Tailwind classes
- Check Network tab to verify assets load from localhost:3000
- Use Responsive Design Mode to test different screen sizes
  `)
}
