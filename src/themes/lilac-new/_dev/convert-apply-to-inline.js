#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to convert @apply directives to inline classes in Vue components
 * This script processes Vue files and moves @apply styles to inline classes
 */

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Mapping of @apply classes to Tailwind utility classes
const applyMappings = {
  // Layout
  'relative': 'relative',
  'absolute': 'absolute',
  'fixed': 'fixed',
  'block': 'block',
  'inline-block': 'inline-block',
  'flex': 'flex',
  'inline-flex': 'inline-flex',
  'grid': 'grid',
  'hidden': 'hidden',
  
  // Flexbox
  'flex-col': 'flex-col',
  'flex-row': 'flex-row',
  'flex-wrap': 'flex-wrap',
  'items-center': 'items-center',
  'items-start': 'items-start',
  'items-end': 'items-end',
  'justify-center': 'justify-center',
  'justify-between': 'justify-between',
  'justify-end': 'justify-end',
  'justify-start': 'justify-start',
  
  // Spacing
  'p-0': 'p-0', 'p-1': 'p-1', 'p-2': 'p-2', 'p-3': 'p-3', 'p-4': 'p-4', 'p-5': 'p-5', 'p-6': 'p-6',
  'm-0': 'm-0', 'm-1': 'm-1', 'm-2': 'm-2', 'm-3': 'm-3', 'm-4': 'm-4', 'm-5': 'm-5', 'm-6': 'm-6',
  'px-2': 'px-2', 'px-3': 'px-3', 'px-4': 'px-4', 'px-6': 'px-6',
  'py-2': 'py-2', 'py-3': 'py-3', 'py-4': 'py-4', 'py-6': 'py-6',
  'mt-1': 'mt-1', 'mt-2': 'mt-2', 'mt-3': 'mt-3', 'mb-1': 'mb-1', 'mb-2': 'mb-2', 'mb-3': 'mb-3',
  
  // Sizing
  'w-full': 'w-full', 'w-6': 'w-6', 'w-20': 'w-20', 'w-4': 'w-4',
  'h-auto': 'h-auto', 'h-6': 'h-6', 'h-4': 'h-4',
  'max-w-full': 'max-w-full', 'max-w-sm': 'max-w-sm', 'max-w-md': 'max-w-md', 'max-w-2xl': 'max-w-2xl', 'max-w-4xl': 'max-w-4xl',
  'max-h-full': 'max-h-full',
  
  // Colors
  'bg-white': 'bg-white', 'bg-gray-50': 'bg-gray-50', 'bg-gray-100': 'bg-gray-100', 'bg-gray-200': 'bg-gray-200',
  'bg-brand-primary': 'bg-brand-primary', 'bg-green-500': 'bg-green-500', 'bg-red-500': 'bg-red-500', 'bg-blue-500': 'bg-blue-500',
  'text-white': 'text-white', 'text-gray-900': 'text-gray-900', 'text-gray-800': 'text-gray-800', 'text-gray-700': 'text-gray-700',
  'text-gray-600': 'text-gray-600', 'text-gray-500': 'text-gray-500', 'text-gray-400': 'text-gray-400',
  'text-brand-primary': 'text-brand-primary', 'text-red-600': 'text-red-600',
  
  // Typography
  'text-sm': 'text-sm', 'text-base': 'text-base', 'text-lg': 'text-lg', 'text-xl': 'text-xl',
  'font-medium': 'font-medium', 'font-semibold': 'font-semibold', 'font-bold': 'font-bold',
  'text-center': 'text-center', 'text-left': 'text-left', 'text-right': 'text-right',
  'uppercase': 'uppercase', 'normal-case': 'normal-case',
  'line-through': 'line-through',
  
  // Borders
  'border': 'border', 'border-0': 'border-0', 'border-t': 'border-t', 'border-b': 'border-b',
  'border-gray-200': 'border-gray-200', 'border-gray-300': 'border-gray-300',
  'rounded': 'rounded', 'rounded-md': 'rounded-md', 'rounded-lg': 'rounded-lg', 'rounded-full': 'rounded-full',
  'rounded-t-lg': 'rounded-t-lg', 'rounded-l': 'rounded-l', 'rounded-r': 'rounded-r',
  
  // Effects
  'shadow-sm': 'shadow-sm', 'shadow-md': 'shadow-md', 'shadow-lg': 'shadow-lg',
  'opacity-0': 'opacity-0', 'opacity-50': 'opacity-50', 'opacity-75': 'opacity-75', 'opacity-100': 'opacity-100',
  
  // Positioning
  'top-0': 'top-0', 'left-0': 'left-0', 'right-0': 'right-0', 'bottom-0': 'bottom-0',
  'top-2': 'top-2', 'left-2': 'left-2', 'right-2': 'right-2', 'bottom-2': 'bottom-2',
  'top-full': 'top-full', 'left-1/2': 'left-1/2',
  
  // Z-index
  'z-10': 'z-10', 'z-50': 'z-50',
  
  // Transforms
  'transform': 'transform', 'scale-95': 'scale-95', 'scale-100': 'scale-100',
  'translate-y-0': 'translate-y-0', 'translate-y-2': 'translate-y-2', '-translate-y-2': '-translate-y-2',
  '-translate-x-1/2': '-translate-x-1/2',
  
  // Transitions
  'transition-all': 'transition-all', 'transition-colors': 'transition-colors', 'transition-opacity': 'transition-opacity',
  'transition-shadow': 'transition-shadow', 'transition-transform': 'transition-transform',
  'duration-200': 'duration-200', 'duration-300': 'duration-300',
  'ease-out': 'ease-out',
  
  // Hover states
  'hover:bg-gray-50': 'hover:bg-gray-50', 'hover:bg-gray-100': 'hover:bg-gray-100', 'hover:bg-gray-300': 'hover:bg-gray-300',
  'hover:bg-blue-700': 'hover:bg-blue-700', 'hover:text-brand-primary': 'hover:text-brand-primary',
  'hover:text-gray-600': 'hover:text-gray-600', 'hover:text-gray-900': 'hover:text-gray-900',
  'hover:shadow-md': 'hover:shadow-md', 'hover:scale-105': 'hover:scale-105',
  'hover:border-gray-400': 'hover:border-gray-400',
  
  // Focus states
  'focus:outline-none': 'focus:outline-none', 'focus:ring-2': 'focus:ring-2', 'focus:ring-brand-primary': 'focus:ring-brand-primary',
  'focus:border-brand-primary': 'focus:border-brand-primary',
  
  // Disabled states
  'disabled:opacity-50': 'disabled:opacity-50', 'disabled:cursor-not-allowed': 'disabled:cursor-not-allowed',
  
  // Lists
  'list-none': 'list-none',
  
  // Overflow
  'overflow-hidden': 'overflow-hidden', 'overflow-y-auto': 'overflow-y-auto',
  
  // Object fit
  'object-cover': 'object-cover',
  
  // Visibility
  'visible': 'visible', 'invisible': 'invisible',
  
  // Screen reader
  'sr-only': 'sr-only',
  
  // Text decoration
  'no-underline': 'no-underline',
  
  // Gaps
  'gap-1': 'gap-1', 'gap-2': 'gap-2', 'gap-3': 'gap-3',
  
  // Flex
  'flex-1': 'flex-1'
};

function convertApplyToInline(content) {
  // This is a simplified conversion - in practice, you'd need more sophisticated parsing
  // For now, we'll just remove the style sections with @apply directives
  // and provide guidance on manual conversion
  
  // Remove style sections that contain @apply
  const styleRegex = /<style[^>]*scoped[^>]*>[\s\S]*?<\/style>/g;
  
  return content.replace(styleRegex, (match) => {
    if (match.includes('@apply')) {
      console.log('Found @apply directives in style section - manual conversion needed');
      return ''; // Remove the entire style section
    }
    return match; // Keep non-@apply styles
  });
}

async function processVueFiles() {
  const componentDir = 'src/components';
  const vueFiles = glob.sync(`${componentDir}/**/*.vue`);
  
  console.log(`Found ${vueFiles.length} Vue files to process`);
  
  for (const file of vueFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      if (content.includes('@apply')) {
        console.log(`Processing: ${file}`);
        
        // For now, just log which files need manual conversion
        console.log(`  - Contains @apply directives - needs manual conversion`);
        
        // You could implement automatic conversion here
        // const converted = convertApplyToInline(content);
        // fs.writeFileSync(file, converted);
      }
    } catch (error) {
      console.error(`Error processing ${file}:`, error.message);
    }
  }
}

if (require.main === module) {
  processVueFiles().catch(console.error);
}

module.exports = { convertApplyToInline, applyMappings };
