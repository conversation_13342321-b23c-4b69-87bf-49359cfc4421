import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import tailwindTemplatesPlugin from './plugins/tailwind-templates.js'

export default defineConfig({
  plugins: [
    vue(),
    tailwindTemplatesPlugin()
  ],
  
  // Build configuration
  build: {
    outDir: '../assets',
    emptyOutDir: false, // Don't empty the entire assets directory
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/main.js')
      },
      output: {
        entryFileNames: 'js/theme.js',
        chunkFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.names && assetInfo.names[0] && assetInfo.names[0].endsWith('.css')) {
            return 'css/theme.css'
          }
          return 'assets/[name]-[hash][extname]'
        }
      }
    },
    
    // Generate manifest for PrestaShop integration
    manifest: true,
    
    // Source maps for development
    sourcemap: process.env.NODE_ENV === 'development'
  },
  
  // Development server configuration
  server: {
    port: 3000,
    host: true,
    cors: true,
    hmr: {
      port: 3000
    }
  },
  
  // CSS configuration
  css: {
    postcss: './postcss.config.js'
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      'vue': 'vue/dist/vue.esm-bundler.js'
    }
  },
  
  // Define global constants
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
})
