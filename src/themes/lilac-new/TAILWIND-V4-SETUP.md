# Tailwind CSS v4 Setup (PostCSS-Free)

This document explains the Tailwind CSS v4 configuration without PostCSS dependencies.

## 🎯 Overview

The theme now uses **Tailwind CSS v4** exclusively, with all PostCSS dependencies removed for a cleaner, simpler build process.

## 🗑️ Removed Dependencies

### PostCSS-Related Packages
- `postcss` - PostCSS processor
- `autoprefixer` - CSS vendor prefixing
- `@tailwindcss/forms` - Form plugin (v3 only)
- `@tailwindcss/typography` - Typography plugin (v3 only)
- `@tailwindcss/aspect-ratio` - Aspect ratio plugin (v3 only)

### Configuration Files
- `postcss.config.js` - PostCSS configuration
- `scripts/extract-classes.js` - Class extraction script
- `plugins/tailwind-templates.js` - Custom PostCSS plugin

## ✅ Current Setup

### Package Dependencies
```json
{
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.2.3",
    "@tailwindcss/vite": "^4.1.6",
    "tailwindcss": "^4.1.6",
    "vite": "^6.3.5",
    "glob": "^10.3.0"
  }
}
```

### Vite Configuration
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    vue(),
    tailwindcss()  // Tailwind v4 Vite plugin
  ]
})
```

### CSS Entry Point
```css
/* src/main.css */
@import "tailwindcss";

/* Custom component styles */
.btn {
  padding: 1rem 1.5rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease-in-out;
}
```

### Tailwind Configuration
```javascript
// tailwind.config.js
export default {
  content: [
    './src/**/*.{vue,js,ts,jsx,tsx}',
    '../templates/**/*.{tpl,html}',
    '../modules/**/*.{tpl,html}',
    // ... template paths
  ],
  theme: {
    extend: {
      colors: {
        'brand-primary': '#007cba',
        'brand-secondary': '#6c757d',
        'brand-accent': '#28a745'
      }
    }
  }
}
```

## 🔄 Migration Changes

### 1. **CSS Syntax**
**Before (Tailwind v3 with PostCSS):**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn {
    @apply px-4 py-2 font-medium rounded;
  }
}
```

**After (Tailwind v4):**
```css
@import "tailwindcss";

.btn {
  padding: 1rem 1.5rem;
  font-weight: 500;
  border-radius: 0.375rem;
}
```

### 2. **Vue Component Styles**
**Before (with @apply):**
```vue
<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center;
}
</style>
```

**After (pure CSS):**
```vue
<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
```

### 3. **Removed Classes**
- `inset-0` → `top-0 left-0 right-0 bottom-0`
- All `@apply` directives → Pure CSS properties

## 🛠️ Development Workflow

### **Start Development**
```bash
cd _dev
pnpm dev
```

### **Build for Production**
```bash
cd _dev
pnpm build
```

### **Watch Mode**
```bash
cd _dev
pnpm watch
```

## 🎨 Styling Approach

### **Utility Classes in Templates**
```smarty
{* Use Tailwind utilities directly in templates *}
<div class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg">
  <h3 class="text-lg font-semibold text-gray-900">Title</h3>
  <button class="px-4 py-2 bg-brand-primary text-white rounded hover:bg-blue-700">
    Button
  </button>
</div>
```

### **Component Styles in Vue**
```vue
<style scoped>
/* Use pure CSS for component-specific styles */
.custom-component {
  background: linear-gradient(to right, #007cba, #0056b3);
  border-radius: 0.5rem;
  padding: 1rem;
}

.custom-component:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style>
```

### **Global Styles in main.css**
```css
/* Define reusable component classes */
.btn {
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  background-color: #007cba;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}
```

## 🔍 Benefits

### **Simplified Build Process**
- ✅ No PostCSS configuration needed
- ✅ Fewer dependencies to manage
- ✅ Faster build times
- ✅ Less complexity

### **Better Performance**
- ✅ Direct Tailwind v4 integration
- ✅ Optimized CSS generation
- ✅ Smaller bundle sizes
- ✅ Faster hot reload

### **Cleaner Code**
- ✅ Pure CSS instead of @apply directives
- ✅ Better IDE support
- ✅ Easier debugging
- ✅ More explicit styling

## 🚨 Important Notes

### **Template Classes**
Tailwind utility classes in `.tpl` files work automatically thanks to the content configuration:

```smarty
{* These classes are automatically detected and included *}
<div class="h-[40px] bg-white border border-gray-200 rounded-lg p-4">
  <span class="text-sm text-gray-600">Content</span>
</div>
```

### **Custom Properties**
For complex styles, use CSS custom properties:

```css
.theme-component {
  --primary-color: #007cba;
  --border-radius: 0.5rem;
  
  background-color: var(--primary-color);
  border-radius: var(--border-radius);
}
```

### **Responsive Design**
Use Tailwind's responsive utilities:

```smarty
<div class="block sm:hidden md:flex lg:grid xl:block">
  Responsive content
</div>
```

## 🎉 Result

The theme now has:
- **Cleaner configuration** with fewer dependencies
- **Faster builds** without PostCSS overhead
- **Better maintainability** with pure CSS
- **Full Tailwind v4 features** and optimizations
- **Seamless template integration** for `.tpl` files

All functionality is preserved while providing a more modern and efficient development experience!
